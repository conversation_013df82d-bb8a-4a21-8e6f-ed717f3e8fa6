/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateCategoryStatus } from '@/services/CategoriesService'

export const useUpdateCategoryStatus = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, { id: string | number; status: number }>({
    mutationFn: ({ id, status }) => updateCategoryStatus(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['categories'] })
      queryClient.invalidateQueries({ queryKey: ['category', id] })
    },
    onError: (error) => {
      console.log('Error updating category status:', error)
    },
  })
}
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouseShelf } from '@/services/Warehouses'

export const useUpdateWarehouseShelf = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        {
            warehouseId: string
            areaId: string
            unitId: string
            shelfId: string
            shelf: { length: number; width: number; height: number }
        }
    >({
        mutationFn: ({ warehouseId, areaId, unitId, shelfId, shelf }) =>
            updateWarehouseShelf(warehouseId, areaId, unitId, shelfId, {
                length: shelf.length,
                width: shelf.width,
                maxBoxes: shelf.height,
            }),
        onSuccess: (_, { warehouseId, areaId, unitId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: [
                    'warehouse',
                    warehouseId,
                    'area',
                    areaId,
                    'unit',
                    unitId,
                ],
            })
        },
        onError: (error) => {
            console.log('Error updating warehouse shelf:', error)
        },
    })
}

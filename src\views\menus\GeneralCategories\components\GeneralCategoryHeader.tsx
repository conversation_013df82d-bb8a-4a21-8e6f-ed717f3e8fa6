import React from 'react'
import useTranslation from '@/utils/hooks/useTranslation'
import { Button } from '@/components/ui'
import { TbPlus } from 'react-icons/tb'

interface GeneralCategoryHeaderProps {
    onAddGeneralCategory: () => void
    onColumnVisibilityChange?: (columns: string[]) => void
}

const GeneralCategoryHeader = ({ onAddGeneralCategory }: GeneralCategoryHeaderProps) => {
    const { t } = useTranslation()

    return (
        <Button
            variant="solid"
            size="sm"
            icon={<TbPlus className="text-xl" />}
            className="shadow-sm hover:shadow-md transition-all duration-200 bg-primary-mild hover:bg-primary-deep"
            onClick={onAddGeneralCategory}
        >
            {t('nav.generalCategories.addGeneralCategory')}
        </Button>
    )
}

export default GeneralCategoryHeader

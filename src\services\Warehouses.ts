import ApiService from '@/services/ApiService'
import {
    area,
    Unit,
    Warehouse,
    WarehouseDetails,
    WarehouseForm,
    WarehouseShelf,
    WarehouseStructure,
    WarehouseUnit,
} from '@/@types/warehouse'
import { Status } from '@/@types/common'

// GET /api/Warehouses - Get list of all warehouses with basic information
export async function getWarehouses() {
    return ApiService.get<Warehouse[]>('/Warehouses')
}

// GET /api/Warehouses/{id} - Get detailed information for a specific warehouse by ID
export async function getWarehouseById(id: string) {
    return ApiService.get<WarehouseDetails>(`/Warehouses/${id}`)
}

// POST /api/Warehouses - Create a new warehouse record
export async function createWarehouse(warehouse: WarehouseForm) {
    return ApiService.post('/Warehouses', warehouse)
}

// PUT /api/Warehouses/{id} - Update an existing warehouse record
export async function updateWarehouse(id: string, warehouse: WarehouseForm) {
    return ApiService.put<Warehouse>(`/Warehouses/${id}`, warehouse)
}

// DELETE /api/Warehouses/{id} - Delete a warehouse record (soft delete)
export async function deleteWarehouse(id: string) {
    return ApiService.delete<void>(`/Warehouses/${id}`)
}

// PATCH /api/Warehouses/{id}/status - Update the approval status of a warehouse
export async function updateWarehouseStatus(
    id: string,
    statusUpdate: { newStatus: Status },
) {
    return ApiService.patch(`/Warehouses/${id}/status`, statusUpdate)
}

export async function generateWarehouseStructure(
    id: string,
    structure: WarehouseStructure,
) {
    return ApiService.post(`/Warehouses/${id}/generate-structure`, structure)
}

export async function addUnitsToArea(
    warehouseId: string,
    areaId: string,
    units: WarehouseUnit,
) {
    return ApiService.post(
        `/Warehouses/${warehouseId}/areas/${areaId}/units`,
        units,
    )
}

export async function addShelvesToUnit(
    warehouseId: string,
    areaId: string,
    unitId: string,
    shelves: WarehouseShelf,
) {
    return ApiService.post(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}/shelves`,
        shelves,
    )
}

export async function getWarehouseArea(warehouseId: string, areaId: string) {
    return ApiService.get<area>(`/Warehouses/${warehouseId}/areas/${areaId}`)
}

export async function getWarehouseUnit(
    warehouseId: string,
    areaId: string,
    unitId: string,
) {
    return ApiService.get<Unit>(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}`,
    )
}

export async function updateWarehouseUnit(
    warehouseId: string,
    areaId: string,
    unitId: string,
    unit: {
        length: number
        width: number
    },
) {
    return ApiService.put(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}`,
        unit,
    )
}

export async function deleteWarehouseUnit(
    warehouseId: string,
    areaId: string,
    unitId: string,
) {
    return ApiService.delete<void>(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}`,
    )
}

export async function updateWarehouseArea(
    warehouseId: string,
    areaId: string,
    area: {
        length: number
        width: number
    },
) {
    return ApiService.put(`/Warehouses/areas/${warehouseId}/${areaId}`, area)
}

export async function deleteWarehouseArea(warehouseId: string, areaId: string) {
    return ApiService.delete<void>(`/Warehouses/areas/${warehouseId}/${areaId}`)
}

export async function updateWarehouseShelf(
    warehouseId: string,
    areaId: string,
    unitId: string,
    shelfId: string,
    shelf: {
        length: number
        width: number
        maxBoxes: number
    },
) {
    return ApiService.put<WarehouseShelf>(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}/shelves/${shelfId}`,
        shelf,
    )
}

export async function deleteWarehouseShelf(
    warehouseId: string,
    areaId: string,
    unitId: string,
    shelfId: string,
) {
    return ApiService.delete<void>(
        `/Warehouses/${warehouseId}/areas/${areaId}/units/${unitId}/shelves/${shelfId}`,
    )
}

export async function transferBoxesToWarehouseApi(
    warehouseId: string,
    transferData: {
        boxes: string[]
        notes: string
    },
) {
    return ApiService.post<void>(
        `/Warehouses/${warehouseId}/transfer`,
        transferData,
    )
}

export interface Document {
    documentId: string
    title: string
    date: string
    status: number
    documentTypeName: string
    pagesCount: number
    boxCode: string
}

export interface DocumentDetails {
    documentId: string
    fileId: string
    fileTitle: string
    fileConfidentiality: number
    fileCategoryId: number
    fileCategoryName: string
    generalCategoryId: number
    generalCategoryName: string
    documentTypeId: number
    documentTypeName: string
    title: string
    date: string
    status: number
    url: string
    pagesCount: number
    boxCode: string
    organizationalNodeId: string
    organizationalNodeName: string
    followUpDate: string
    notes: string
    keywords: Keyword[]
    attachments: Attachment[]
}

interface Keyword {
    id: number
    value: string
    numberOfTimesUsed: number
}

interface Attachment {
    id: number
    documentId: string
    url: string
    createdAt: string
}

export interface DocumentCreate {
    fileId: string
    generalCategoryId: number
    documentTypeId: number
    title: string
    date: string
}

export interface DocumentUpdate {
    organizationalNodeId: string
    followUpDate: string
    notes: string
    keywordIds: number[]
}

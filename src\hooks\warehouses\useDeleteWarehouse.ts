import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWarehouse } from '@/services/Warehouses'

export const useDeleteWarehouse = () => {
  const queryClient = useQueryClient()

  return useMutation<void, Error, string>({
    mutationFn: deleteWarehouse,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['warehouses'] })
    },
    onError: (error) => {
      console.log('Error deleting warehouse:', error)
    },
  })
}
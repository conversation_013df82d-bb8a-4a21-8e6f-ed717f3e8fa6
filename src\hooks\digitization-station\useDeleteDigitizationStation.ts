import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteDigitizationStation } from '@/services/DigitizationStationService'

export const useDeleteDigitizationStation = () => {
  const queryClient = useQueryClient()

  return useMutation<void, Error, string | number>({
    mutationFn: deleteDigitizationStation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['digitization-stations'] })
    },
    onError: (error) => {
      console.log('Error deleting digitization station:', error)
    },
  })
}
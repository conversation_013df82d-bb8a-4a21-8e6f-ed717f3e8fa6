import { useQuery } from '@tanstack/react-query'
import { getBoxById } from '@/services/Boxes'
import { BoxDetails } from '@/@types/box'

export const useGetBoxById = (boxId: string | number) => {
    return useQuery<BoxDetails>({
        queryKey: ['box', boxId],
        queryFn: () => getBoxById(boxId),
        enabled: !!boxId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

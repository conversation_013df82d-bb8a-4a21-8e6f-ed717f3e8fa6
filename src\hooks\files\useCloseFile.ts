import { useMutation, useQueryClient } from '@tanstack/react-query'
import { closeFile } from '@/services/FilesService'

export const useCloseFile = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: closeFile,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['files'] })
        },
        onError: (error) => {
            console.log('Error updating file status:', error)
        },
    })
}

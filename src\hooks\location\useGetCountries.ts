import { useQuery } from '@tanstack/react-query'
import { getCountries } from '@/services/LocationService'
import type { Country } from '@/@types/location'

export const useGetCountries = () => {
  return useQuery<Country[]>({
    queryKey: ['countries'],
    queryFn: getCountries,
    staleTime: 30 * 60 * 1000, // 30 minutes (countries don't change often)
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 3,
  })
}
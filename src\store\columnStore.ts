import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Column {
  key: string
  label: string
  visible: boolean
}

interface ColumnStore {
  columns: Record<string, Column[]>
  getColumns: (tableId: string) => Column[]
  setColumns: (tableId: string, columns: Column[]) => void
  toggleColumn: (tableId: string, columnKey: string) => void
  resetColumns: (tableId: string, defaultColumns: Column[]) => void
  getVisibleColumns: (tableId: string) => Column[]
}

export const useColumnStore = create<ColumnStore>()(
  persist(
    (set, get) => ({
      columns: {},
      
      getColumns: (tableId: string) => {
        return get().columns[tableId] || []
      },
      
      setColumns: (tableId: string, columns: Column[]) => {
        set((state) => ({
          columns: {
            ...state.columns,
            [tableId]: columns
          }
        }))
      },
      
      toggleColumn: (tableId: string, columnKey: string) => {
        set((state) => {
          const tableColumns = state.columns[tableId] || []
          const updatedColumns = tableColumns.map(col => 
            col.key === columnKey ? { ...col, visible: !col.visible } : col
          )
          
          return {
            columns: {
              ...state.columns,
              [tableId]: updatedColumns
            }
          }
        })
      },
      
      resetColumns: (tableId: string, defaultColumns: Column[]) => {
        set((state) => ({
          columns: {
            ...state.columns,
            [tableId]: defaultColumns
          }
        }))
      },
      
      getVisibleColumns: (tableId: string) => {
        const columns = get().columns[tableId] || []
        return columns.filter(col => col.visible)
      }
    }),
    {
      name: 'column-customization-storage',
      partialize: (state) => ({ columns: state.columns })
    }
  )
)
import { useState } from 'react'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import DocumentsModal from './DocumentsModal'
import { TbPlus } from 'react-icons/tb'

interface DocumentsListActionToolsProps {
    fileId: string
}

const DocumentsListActionTools = ({
    fileId,
}: DocumentsListActionToolsProps) => {
    const { t } = useTranslation()
    const [isAddModalOpen, setIsAddModalOpen] = useState(false)

    const handleAddNew = () => {
        setIsAddModalOpen(true)
    }

    const handleAddSuccess = () => {
        setIsAddModalOpen(false)
    }

    const handleAddClose = () => {
        setIsAddModalOpen(false)
    }

    return (
        <>
            <div className="flex items-center gap-2">
                <Button
                    variant="solid"
                    size="sm"
                    icon={<TbPlus />}
                    className="flex items-center gap-2"
                    onClick={handleAddNew}
                >
                    {t('nav.Document.addDocument')}
                </Button>
            </div>

            {/* Add Modal */}
            <DocumentsModal
                isOpen={isAddModalOpen}
                fileId={fileId}
                onSuccess={handleAddSuccess}
                onClose={handleAddClose}
            />
        </>
    )
}

export default DocumentsListActionTools

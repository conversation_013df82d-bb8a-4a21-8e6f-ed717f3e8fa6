import ApiService from '@/services/ApiService'

export interface GeneralCategory {
    id: number
    name: string
}

export interface GeneralCategoryDetails {
    id: number
    name: string
    createdById: string
    createdAt: string
    updatedById: string
    updatedAt: string
}

// Get all general categories
export async function getGeneralCategories() {
    return ApiService.get<GeneralCategory[]>('/GeneralCategories')
}

// Get general category by ID
export async function getGeneralCategoryById(id: number) {
    return ApiService.get<GeneralCategoryDetails>(`/GeneralCategories/${id}`)
}

// Create new general category
export async function createGeneralCategory(name: string) {
    return ApiService.post('/GeneralCategories', { name })
}

// Update general category information
export async function updateGeneralCategory(id: number, name: string) {
    return ApiService.put<GeneralCategory>(`/GeneralCategories/${id}`, { name })
}

// Delete general category
export async function deleteGeneralCategory(id: number) {
    return ApiService.delete(`/GeneralCategories/${id}`)
}

// Delete multiple general categories
export async function deleteMultipleGeneralCategories(ids: number[]) {
    return Promise.all(ids.map((id) => deleteGeneralCategory(id)))
}

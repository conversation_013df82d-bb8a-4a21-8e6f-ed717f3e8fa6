import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateConsumableStatus } from '@/services/ConsumableService'
import type { Status } from '@/@types/common'

export const useUpdateConsumableStatus = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ id, status }: { id: number; status: Status }) => 
            updateConsumableStatus(id, status),
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            queryClient.invalidateQueries({ queryKey: ['consumable', variables.id] })
        },
        onError: (error) => {
            console.error(error)
        },
    })
}
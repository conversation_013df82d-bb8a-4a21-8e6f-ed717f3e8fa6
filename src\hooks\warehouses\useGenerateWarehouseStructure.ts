/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { generateWarehouseStructure } from '@/services/Warehouses'
import type { WarehouseStructure } from '@/@types/warehouse'

export const useGenerateWarehouseStructure = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        { id: string; structure: WarehouseStructure }
    >({
        mutationFn: ({ id, structure }) =>
            generateWarehouseStructure(id, structure),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
        },
        onError: (error) => {
            console.log('Error generating warehouse structure:', error)
        },
    })
}

import { createOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreatedNode } from '@/@types/organizationalStructure'

export const useCreateOrgStructure = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: (data: CreatedNode) => createOrganizeStructure(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
           
        },
        onError: () => {
            
        },
    })
}
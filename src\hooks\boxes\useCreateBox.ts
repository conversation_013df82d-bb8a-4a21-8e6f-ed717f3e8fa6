import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createBox } from '@/services/Boxes'
import { BoxDetails } from '@/@types/box'

export const useCreateBox = () => {
    const queryClient = useQueryClient()

    return useMutation<
        BoxDetails,
        Error,
        { organizationalNodeId: number | string }
    >({
        mutationFn: ({ organizationalNodeId }) =>
            createBox(organizationalNodeId),
        onSuccess: (data) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Optionally set the new box data in cache
            queryClient.setQueryData(['box', data.boxId], data)
        },
        onError: (error) => {
            console.error(
                'Failed to create box:',
                error?.message || 'Unknown error',
            )
        },
    })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteBuilding } from '@/services/BuildingService'

export const useDeleteBuilding = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => deleteBuilding(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] })
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to delete building')
    },
  })
}
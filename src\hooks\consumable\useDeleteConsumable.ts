import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteConsumable } from '@/services/ConsumableService'

export const useDeleteConsumable = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: (id: number) => deleteConsumable(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
        },
        onError: (error) => {
            console.error('Error deleting consumable:', error)
        },
    })
}
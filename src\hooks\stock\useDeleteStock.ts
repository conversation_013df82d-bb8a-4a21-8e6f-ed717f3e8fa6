import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteStock } from '@/services/StockService'

export const useDeleteStock = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, number>({
        mutationFn: deleteStock,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
        },
        onError: (error) => {
            console.log('Error deleting stock:', error)
        },
    })
}

import { BOXES_PREFIX_PATH } from '@/constants/route.constant'
import {
    NAV_ITEM_TYPE_COLLAPSE,
    NAV_ITEM_TYPE_ITEM,
} from '@/constants/navigation.constant'
import type { NavigationTree } from '@/@types/navigation'
import { ADMIN, USER } from '@/constants/roles.constant'

const boxesNavigationConfig: NavigationTree[] = [
    {
        key: 'boxes',
        path: '',
        title: 'Boxes',
        translateKey: 'nav.boxes.boxes',
        icon: 'boxes',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [ADMIN, USER],
        meta: {
            horizontalMenu: {
                layout: 'default',
            },
        },
        subMenu: [
            {
                key: 'boxes.list',
                path: `${BOXES_PREFIX_PATH}`,
                title: 'Boxes List',
                translateKey: 'nav.boxes.boxesList',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [USER],
                subMenu: [],
            },
            {
                key: 'linkFiles.list',
                path: `${BOXES_PREFIX_PATH}/linkFiles`,
                title: 'Link Files to Boxes',
                translateKey: 'nav.linkFiles.linkFilesToBoxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [USER],
                subMenu: [],
            },
            {
                key: 'transferBoxes.list',
                path: `${BOXES_PREFIX_PATH}/transferBoxes`,
                title: 'Transfer Boxes to Warehouse',
                translateKey: 'nav.transferBoxes.transferBoxesToWarehouse',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [USER],
                subMenu: [],
            },
            {
                key: 'boxes.allBoxes',
                path: `${BOXES_PREFIX_PATH}/all`,
                title: 'All Boxes',
                translateKey: 'nav.boxes.allBoxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'boxes.warehouseBoxes',
                path: `${BOXES_PREFIX_PATH}/transferBoxes`,
                title: 'Transfer Boxes',
                translateKey: 'nav.boxes.transferBoxes',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
        ],
    },
]

export default boxesNavigationConfig

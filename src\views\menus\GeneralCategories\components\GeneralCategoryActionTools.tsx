import GeneralCategoriesSearch from './GeneralCategoriesSearch'
import GeneralCategoryFilter from './GeneralCategoryFilter'

interface GeneralCategoryActionToolsProps {
    onColumnVisibilityChange?: (columns: string[]) => void
    onSearch?: (term: string) => void
}

const GeneralCategoryActionTools = ({
    onColumnVisibilityChange,
    onSearch,
}: GeneralCategoryActionToolsProps) => {
    return (
        <div className="flex gap-4 flex-row justify-center items-center ">
            <GeneralCategoriesSearch onInputChange={onSearch} />

            <GeneralCategoryFilter
                onColumnVisibilityChange={onColumnVisibilityChange}
            />
        </div>
    )
}

export default GeneralCategoryActionTools

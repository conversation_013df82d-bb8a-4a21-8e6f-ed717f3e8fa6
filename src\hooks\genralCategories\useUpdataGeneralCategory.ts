import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateGeneralCategory } from '@/services/GeneralCategoriesService'

export const useUpdateGeneralCategory = () => {
    const queryClient = useQueryClient()
    return useMutation({
        mutationFn: (variables: { id: number; name: string }) =>
            updateGeneralCategory(variables.id, variables.name),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
        },
        onError: (error) => {
            console.log('Error updating general category:', error)
        },
    })
}

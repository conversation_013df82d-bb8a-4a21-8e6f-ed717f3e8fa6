import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteMultipleGeneralCategories } from '@/services/GeneralCategoriesService'

export const useDeleteMultipleGeneralCategories = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: deleteMultipleGeneralCategories,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
        },
        onError: (error) => {
            console.error('Error deleting general categories:', error)
        },
    })
}

export default useDeleteMultipleGeneralCategories

import { useQuery } from '@tanstack/react-query'
import { getStockById } from '@/services/StockService'
import type { Stock } from '@/@types/stocks'

export const useGetStockById = (id: number) => {
  return useQuery<Stock>({
    queryKey: ['stock', id],
    queryFn: () => getStockById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouseArea } from '@/services/Warehouses'

export const useUpdateWarehouseArea = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        {
            warehouseId: string
            areaId: string
            area: { length: number; width: number }
        }
    >({
        mutationFn: ({ warehouseId, areaId, area }) =>
            updateWarehouseArea(warehouseId, areaId, area),
        onSuccess: (_, { warehouseId, areaId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId, 'area', areaId],
            })
        },
        onError: (error) => {
            console.log('Error updating warehouse area:', error)
        },
    })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreateUser } from '@/@types/auth'
import { updateUserApi } from '@/services/UsersService'

export const useUpdateUser = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, { id: string; userData: CreateUser }>({
        mutationFn: ({ id, userData }) => updateUserApi(id, userData),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            console.log('User updated successfully')
        },
        onError: (error) => {
            console.log('Error updating user:', error)
        },
    })
}

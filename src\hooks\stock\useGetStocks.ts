import { useQuery } from '@tanstack/react-query'
import { getStocks } from '@/services/StockService'
import type { StockColumns } from '@/@types/stocks'

export const useGetStocks = () => {
    return useQuery<StockColumns[]>({
        queryKey: ['stocks'],
        queryFn: getStocks,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteKeyword<PERSON><PERSON> } from '@/services/Keywords'

export const useDeleteKeyword = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: delete<PERSON><PERSON>word<PERSON><PERSON>,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
        },
        onError: (error) => {
            console.error('Error deleting keyword:', error)
        },
    })
}

export default useDeleteKeyword

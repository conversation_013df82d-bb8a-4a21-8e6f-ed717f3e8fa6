import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import BoxesForm from './BoxesForm'
import useTranslation from '@/utils/hooks/useTranslation'
import { useCreateBox } from '@/hooks/boxes'
import { TbPlus, TbEdit } from 'react-icons/tb'

interface BoxesModalProps {
    isOpen: boolean
    mode: 'create' | 'edit'
    boxId?: string
    onSuccess: () => void
    onClose: () => void
}

const BoxesModal = ({
    isOpen,
    mode,
    boxId,
    onSuccess,
    onClose,
}: BoxesModalProps) => {
    const { t } = useTranslation()
    const { mutate: createBox } = useCreateBox()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleSubmit = async (formData: { organizationalNodeId: number }) => {
        setIsSubmitting(true)
        try {
            if (mode === 'create') {
                await createBox(formData.organizationalNodeId)
                onSuccess()
            } else {
                // Handle edit mode if needed
                console.log('Edit box:', boxId, formData)
                onSuccess()
            }
        } catch (error) {
            console.error(`Failed to ${mode} box:`, error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center gap-2">
                    {mode === 'create' ? (
                        <>
                            <TbPlus className="text-lg" />
                            <h3 className="text-lg font-semibold">
                                {t('nav.boxes.createBox')}
                            </h3>
                        </>
                    ) : (
                        <>
                            <TbEdit className="text-lg" />
                            <h3 className="text-lg font-semibold">
                                {t('nav.boxes.editBox')}
                            </h3>
                        </>
                    )}
                </div>
            </div>

            <div className="p-4">
                <BoxesForm
                    mode={mode}
                    boxId={boxId}
                    isSubmitting={isSubmitting}
                    onSubmit={handleSubmit}
                    onCancel={handleClose}
                />
            </div>
        </Dialog>
    )
}

export default BoxesModal

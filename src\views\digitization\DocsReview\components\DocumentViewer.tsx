import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON>, Button } from '@/components/ui'
import {
    TbFile,
    TbDownload,
    TbZoomIn,
    TbZoomOut,
    TbRotate,
    TbMaximize,
    TbEye,
    TbFileText,
} from 'react-icons/tb'

interface DocumentViewerProps {
    documentUrl?: string
    documentTitle?: string
    documentType?: string
    loading?: boolean
}

export default function DocumentViewer({
    documentUrl,
    documentTitle,
    documentType,
    loading = false,
}: DocumentViewerProps) {
    const { t } = useTranslation()
    const [zoom, setZoom] = useState(100)
    const [rotation, setRotation] = useState(0)
    const [fullscreen, setFullscreen] = useState(false)

    const handleZoomIn = () => setZoom((prev) => Math.min(prev + 25, 200))
    const handleZoomOut = () => setZoom((prev) => Math.max(prev - 25, 50))
    const handleRotate = () => setRotation((prev) => (prev + 90) % 360)
    const handleFullscreen = () => setFullscreen(!fullscreen)

    const handleDownload = () => {
        if (documentUrl) {
            const link = document.createElement('a')
            link.href = documentUrl
            link.download = documentTitle || 'document'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        }
    }

    const renderDocumentPreview = () => {
        if (!documentUrl) {
            return (
                <div className="flex flex-col items-center justify-center h-96 text-gray-500">
                    <TbFile className="text-6xl mb-4" />
                    <p className="text-lg font-medium">
                        {t('viewer.noDocument')}
                    </p>
                    <p className="text-sm">{t('viewer.selectDocument')}</p>
                </div>
            )
        }

        const isImage =
            documentType?.toLowerCase().includes('image') ||
            documentUrl.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)

        const isPdf =
            documentType?.toLowerCase().includes('pdf') ||
            documentUrl.endsWith('.pdf')

        if (isImage) {
            return (
                <div className="flex justify-center items-center min-h-96 p-4">
                    <img
                        src={documentUrl}
                        alt={documentTitle}
                        className="max-w-full max-h-full object-contain shadow-lg"
                        style={{
                            transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
                            transition: 'transform 0.3s ease',
                        }}
                    />
                </div>
            )
        }

        if (isPdf) {
            return (
                <div className="w-full h-96">
                    <iframe
                        src={`${documentUrl}#zoom=${zoom}`}
                        className="w-full h-full border-0"
                        title={documentTitle}
                    />
                </div>
            )
        }

        // For DOCX and other document types
        return (
            <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg">
                <TbFileText className="text-6xl text-blue-500 mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                    {documentTitle}
                </p>
                <p className="text-sm text-gray-500 mb-4">
                    {t('viewer.documentType', {
                        type: documentType || 'Document',
                    })}
                </p>

                <div className="flex gap-3">
                    <Button
                        variant="solid"
                        size="sm"
                        onClick={() => window.open(documentUrl, '_blank')}
                        className="flex items-center gap-2"
                    >
                        <TbEye className="text-sm" />
                        {t('viewer.openInNewTab')}
                    </Button>

                    <Button
                        variant="solid"
                        size="sm"
                        onClick={handleDownload}
                        className="flex items-center gap-2"
                    >
                        <TbDownload className="text-sm" />
                        {t('viewer.download')}
                    </Button>
                </div>

                {/* Office 365 Viewer for DOCX files */}
                {documentUrl &&
                    documentType?.toLowerCase().includes('docx') && (
                        <div className="mt-4 w-full">
                            <iframe
                                src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(documentUrl)}`}
                                className="w-full h-64 border border-gray-300 rounded"
                                title={`${documentTitle} - Office Viewer`}
                            />
                        </div>
                    )}
            </div>
        )
    }

    if (loading) {
        return (
            <Card className="p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded mb-4 w-1/4"></div>
                    <div className="h-96 bg-gray-200 rounded"></div>
                </div>
            </Card>
        )
    }

    return (
        <Card
            className={`${fullscreen ? 'fixed inset-4 z-50' : ''} transition-all duration-300`}
        >
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                        {t('viewer.documentViewer')}
                    </h3>
                    {documentTitle && (
                        <p className="text-sm text-gray-600">{documentTitle}</p>
                    )}
                </div>

                <div className="flex items-center gap-2">
                    {documentUrl && (
                        <>
                            <Button
                                variant="plain"
                                size="sm"
                                onClick={handleZoomOut}
                                disabled={zoom <= 50}
                                title={t('viewer.zoomOut')}
                            >
                                <TbZoomOut />
                            </Button>

                            <span className="text-sm text-gray-600 min-w-12 text-center">
                                {zoom}%
                            </span>

                            <Button
                                variant="plain"
                                size="sm"
                                onClick={handleZoomIn}
                                disabled={zoom >= 200}
                                title={t('viewer.zoomIn')}
                            >
                                <TbZoomIn />
                            </Button>

                            <div className="w-px h-6 bg-gray-300 mx-2"></div>

                            <Button
                                variant="plain"
                                size="sm"
                                onClick={handleRotate}
                                title={t('viewer.rotate')}
                            >
                                <TbRotate />
                            </Button>

                            <Button
                                variant="plain"
                                size="sm"
                                onClick={handleDownload}
                                title={t('viewer.download')}
                            >
                                <TbDownload />
                            </Button>
                        </>
                    )}

                    <Button
                        variant="plain"
                        size="sm"
                        onClick={handleFullscreen}
                        title={
                            fullscreen
                                ? t('viewer.exitFullscreen')
                                : t('viewer.fullscreen')
                        }
                    >
                        <TbMaximize />
                    </Button>
                </div>
            </div>

            <div
                className={`${fullscreen ? 'h-full' : 'max-h-96'} overflow-auto`}
            >
                {renderDocumentPreview()}
            </div>

            {fullscreen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 -z-10"
                    onClick={handleFullscreen}
                />
            )}
        </Card>
    )
}

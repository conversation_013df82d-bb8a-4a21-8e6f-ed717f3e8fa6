import { CreateUser, User } from '@/@types/auth'
import ApiService from '@/services/ApiService'
import { Status } from '@/@types/common'

const BASE_URL = '/Users'

export async function getUsersApi() {
    return ApiService.get<User[]>(`${BASE_URL}`)
}

export async function getUsersByOrganizationalNodeApi(
    organizationalNodeCode: string,
) {
    return ApiService.get<
        Array<{
            name: string
            code: string
        }>
    >(`${BASE_URL}/by-organization/${organizationalNodeCode}`)
}

export async function createNewUserApi(data: CreateUser) {
    return ApiService.post<void>(`${BASE_URL}/users`, { data })
}

export async function updateUserApi(id: string, data: CreateUser) {
    return ApiService.put<void>(`${BASE_URL}/users/${id}`, { data })
}

export async function updateUserStatusApi(userId: string, status: Status) {
    return ApiService.patch<void>(`${BASE_URL}/users/${userId}/status`, {
        status,
    })
}

export async function resetUserPasswordApi(userId: string) {
    return ApiService.post<void>(`${BASE_URL}/${userId}/reset-password`)
}

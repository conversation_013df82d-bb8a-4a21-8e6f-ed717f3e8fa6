import { useMutation, useQueryClient } from '@tanstack/react-query'
import { uploadDocumentPdfApi } from '@/services/DocumentsService'

export const useUploadDocumentPdf = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({
            documentId,
            pdfFile,
        }: {
            documentId: string
            pdfFile: File
        }) => uploadDocumentPdfApi(documentId, pdfFile),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
        },
        onError: (error) => {
            console.log(error?.message || 'Failed to upload document PDF')
        },
    })
}

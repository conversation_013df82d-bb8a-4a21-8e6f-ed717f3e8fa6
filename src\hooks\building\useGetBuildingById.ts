import { useQuery } from '@tanstack/react-query'
import { getBuildingById } from '@/services/BuildingService'
import { BuildingDetails } from '@/@types/building'

export const useGetBuildingById = (id: number) => {
  return useQuery<BuildingDetails>({
    queryKey: ['building', id],
    queryFn: () => getBuildingById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
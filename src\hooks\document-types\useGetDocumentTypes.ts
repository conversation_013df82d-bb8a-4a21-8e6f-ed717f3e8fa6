import { useQuery } from '@tanstack/react-query'
import { getDocumentTypes } from '@/services/DocumentTypesService'
import type { DocumentType } from '@/services/DocumentTypesService'

export const useGetDocumentTypes = () => {
  return useQuery<DocumentType[]>({
    queryKey: ['document-types'],
    queryFn: getDocumentTypes,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 3,
  })
}
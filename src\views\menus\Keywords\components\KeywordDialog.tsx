import React, { useEffect } from 'react'
import Dialog from '@/components/ui/Dialog'
import { Form, FormContainer, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import { useGetKeywords, useCreateKeyword, useUpdateKeyword } from '@/hooks/keywords'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import useTranslation from '@/utils/hooks/useTranslation'

interface KeywordDialogProps {
    isOpen: boolean
    keywordId?: number | null
    onClose: () => void
}

type FormData = {
    value: string
}

const KeywordDialog = ({ isOpen, keywordId, onClose }: KeywordDialogProps) => {
    const { t } = useTranslation()
    const { data: keywords = [] } = useGetKeywords()
    const createKeywordMutation = useCreateKeyword()
    const updateKeywordMutation = useUpdateKeyword()

    const schema = z.object({
        value: z
            .string()
            .min(1, t('nav.keywords.keywordRequired'))
            .max(100, t('nav.keywords.keywordTooLong')),
    })

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            value: '',
        },
    })

    // Helper function to find keyword by ID
    const getKeywordById = (id: number) => {
        return keywords.find(keyword => keyword.id === id)
    }

    // Load keyword data when editing
    useEffect(() => {
        if (keywordId && isOpen) {
            const keyword = getKeywordById(keywordId)
            if (keyword) {
                setValue('value', keyword.value)
            }
        } else if (isOpen) {
            reset({ value: '' })
        }
    }, [keywordId, isOpen, keywords, setValue, reset])

    const onSubmit = async (data: FormData) => {
        try {
            if (keywordId) {
                // Update existing keyword
                await updateKeywordMutation.mutateAsync({
                    id: keywordId,
                    value: data.value,
                })
            } else {
                // Create new keyword
                await createKeywordMutation.mutateAsync(data.value)
            }
            onClose()
            reset()
        } catch (error) {
            console.error('Error saving keyword:', error)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    const isLoading = createKeywordMutation.isPending || updateKeywordMutation.isPending

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="p-6">
                <h4 className="mb-6 text-lg font-semibold">
                    {keywordId
                        ? t('nav.keywords.editKeyword')
                        : t('nav.keywords.addKeyword')}
                </h4>

                <Form onSubmit={handleSubmit(onSubmit)}>
                    <FormContainer>
                        <FormItem
                            label={t('nav.keywords.keyword')}
                            invalid={!!errors.value}
                            errorMessage={errors.value?.message}
                        >
                            <Input
                                {...register('value')}
                                placeholder={t('nav.keywords.enterKeyword')}
                                autoFocus
                            />
                        </FormItem>

                        <div className="flex justify-end gap-2 mt-6">
                            <Button
                                type="button"
                                variant="plain"
                                onClick={handleClose}
                                disabled={isLoading}
                            >
                                {t('nav.shared.cancel')}
                            </Button>
                            <Button
                                type="submit"
                                variant="solid"
                                loading={isLoading}
                                disabled={isLoading}
                            >
                                {keywordId
                                    ? t('nav.shared.update')
                                    : t('nav.shared.create')}
                            </Button>
                        </div>
                    </FormContainer>
                </Form>
            </div>
        </Dialog>
    )
}

export default KeywordDialog
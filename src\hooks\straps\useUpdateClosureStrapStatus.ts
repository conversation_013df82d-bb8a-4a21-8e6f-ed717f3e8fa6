import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateClosureStrapStatus } from '@/services/ConsumableService'
import type { ClosureStrapStatusUpdate } from '@/@types/consumable'

export const useUpdateClosureStrapStatus = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ closureStrapId, statusUpdate }: { closureStrapId: number; statusUpdate: ClosureStrapStatusUpdate }) => 
            updateClosureStrapStatus(closureStrapId, statusUpdate),
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['closure-strap', variables.closureStrapId] })
            queryClient.invalidateQueries({ queryKey: ['closure-straps'] })
        },
        onError: (error) => {
            console.error(error)
        },
    })
}
import { useQuery } from '@tanstack/react-query'
import { getClosureStrapsByConsumable } from '@/services/ConsumableService'

export const useGetClosureStrapsByConsumable = (consumableId: number) => {
    return useQuery({
        queryKey: ['closure-straps', 'by-consumable', consumableId],
        queryFn: () => getClosureStrapsByConsumable(consumableId),
        enabled: !!consumableId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}
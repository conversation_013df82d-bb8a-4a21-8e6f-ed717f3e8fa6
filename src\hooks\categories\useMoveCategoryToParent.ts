/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { moveCategoryToParent } from '@/services/CategoriesService'

export const useMoveCategoryToParent = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, { id: string | number; parentId: string | number | null }>({
    mutationFn: ({ id, parentId }) => moveCategoryToParent(id, parentId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['categories'] })
      queryClient.invalidateQueries({ queryKey: ['category', id] })
    },
    onError: (error) => {
      console.log('Error moving category:', error)
    },
  })
}
import { useQuery } from '@tanstack/react-query'
import { getBoxesByOrganization } from '@/services/Boxes'
import { BoxesResponse } from '@/@types/box'
import { PaginationResponse } from '@/@types/global'

interface UseGetBoxesByOrganizationParams {
    organizationalNodeId: string
    pageNumber?: number
    pageSize?: number
    status?: number | undefined
}

export const useGetBoxesByOrganization = ({
    organizationalNodeId,
    pageNumber = 1,
    pageSize = 10,
    status = undefined,
}: UseGetBoxesByOrganizationParams) => {
    const query = useQuery<BoxesResponse>({
        queryKey: [
            'boxes',
            'organization',
            organizationalNodeId,
            { pageNumber, pageSize, status },
        ],
        queryFn: () =>
            getBoxesByOrganization(organizationalNodeId, {
                pageNumber,
                pageSize,
                boxStatus: status,
            }),
        enabled: !!organizationalNodeId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })

    return {
        ...query,
        Boxes: query.data?.items,
        pagination: query.data as PaginationResponse,
    }
}

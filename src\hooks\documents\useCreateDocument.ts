import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDocument } from '@/services/DocumentsService'
import { DocumentCreate } from '@/@types/document'

export const useCreateDocument = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ document }: { document: DocumentCreate }) =>
            createDocument(document),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
        },
        onError: (error) => {
            console.log(error?.message || 'Failed to create document')
        },
    })
}

import { Card } from '@/components/ui'
import { useRef, useEffect } from 'react'
import Barcode from 'react-barcode'
import { useReactToPrint } from 'react-to-print'

interface PrintBarCodeProps {
    fileId?: string
    onClose?: () => void
}

export default function PrintBarCode({
    fileId = '123456789012',
    onClose,
}: PrintBarCodeProps) {
    const componentRef = useRef<HTMLDivElement>(null)
    const handlePrint = useReactToPrint({
        contentRef: componentRef,
        onAfterPrint: () => {
            onClose?.()
        },
    })

    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    })

    useEffect(() => {
        // Auto-trigger print when component mounts
        const timer = setTimeout(() => {
            handlePrint()
        }, 100)

        return () => clearTimeout(timer)
    }, [handlePrint])

    return (
        <div className="hidden">
            <div ref={componentRef} className="print:block">
                <Card className="w-fit mx-auto p-6 bg-white mt-10">
                    <div className="text-center space-y-4">
                        {/* Title */}
                        <h2 className="text-xl font-bold text-gray-800 mb-4">
                            Document Barcode
                        </h2>

                        {/* Barcode */}
                        <div className="flex justify-center mb-4">
                            <Barcode
                                value={fileId}
                                width={2}
                                height={60}
                                fontSize={14}
                                margin={5}
                                background="#ffffff"
                                lineColor="#000000"
                            />
                        </div>

                        {/* Basic Info */}
                        <div className="space-y-2 text-sm text-gray-600">
                            <p>
                                <span className="font-medium">File ID:</span>{' '}
                                <span className="font-mono">{fileId}</span>
                            </p>
                            <p>
                                <span className="font-medium">Date:</span>{' '}
                                {currentDate}
                            </p>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    )
}

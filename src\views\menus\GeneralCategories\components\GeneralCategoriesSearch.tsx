import React, { useState } from 'react'
import { Input } from '@/components/ui'
import { TbSearch } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'

interface GeneralCategoriesSearchProps {
    onInputChange?: (value: string) => void
}

const GeneralCategoriesSearch = ({ onInputChange }: GeneralCategoriesSearchProps) => {
    const { t } = useTranslation()
    const [searchValue, setSearchValue] = useState('')

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setSearchValue(value)
        onInputChange?.(value)
    }

    return (
        <div className="flex-1">
            <Input
                placeholder={t('nav.generalCategories.searchGeneralCategories')}
                value={searchValue}
                prefix={<TbSearch className="text-lg" />}
                className="w-full"
                onChange={handleChange}
            />
        </div>
    )
}

export default GeneralCategoriesSearch

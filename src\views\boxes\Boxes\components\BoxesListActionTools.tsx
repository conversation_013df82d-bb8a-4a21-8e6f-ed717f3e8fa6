import Button from '@/components/ui/Button'
import { useCreateBox } from '@/hooks/boxes'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbPlus } from 'react-icons/tb'

const BoxesListActionTools = ({
    organizationalNodeId,
}: {
    organizationalNodeId: string
}) => {
    const { t } = useTranslation()

    const { mutate: createBox, isPending } = useCreateBox()

    const onCreateBox = () => {
        createBox({ organizationalNodeId: organizationalNodeId })
    }

    return (
        <div className="flex items-center gap-2">
            <Button
                variant="solid"
                size="sm"
                icon={<TbPlus />}
                className="flex items-center gap-2"
                disabled={isPending}
                onClick={onCreateBox}
            >
                {t('nav.boxes.createBox')}
            </Button>
        </div>
    )
}

export default BoxesListActionTools

import React from 'react'
import useTranslation from '@/utils/hooks/useTranslation'

interface StatusProps {
    row: {
        original: {
            status: number
        }
    }
}

const Status: React.FC<StatusProps> = ({ row }) => {
    const { t } = useTranslation()
    const status = row.original.status

    const getStatusConfig = (status: number) => {
        switch (status) {
            case 1:
                return {
                    text: t('nav.shared.active') || 'Active',
                    bgColor: 'bg-emerald-100 dark:bg-emerald-900',
                    textColor: 'text-emerald-800 dark:text-emerald-200',
                    borderColor: 'border-emerald-200 dark:border-emerald-700'
                }
            case 2:
                return {
                    text: t('nav.shared.frozen') || 'Frozen',
                    bgColor: 'bg-blue-100 dark:bg-blue-900',
                    textColor: 'text-blue-800 dark:text-blue-200',
                    borderColor: 'border-blue-200 dark:border-blue-700'
                }
            default:
                return {
                    text: t('nav.shared.inactive') || 'Inactive',
                    bgColor: 'bg-red-100 dark:bg-red-900',
                    textColor: 'text-red-800 dark:text-red-200',
                    borderColor: 'border-red-200 dark:border-red-700'
                }
        }
    }

    const statusConfig = getStatusConfig(status)

    return (
        <div className="flex items-center justify-center">
            <span
                className={`px-3 py-1 rounded-full text-sm font-medium border ${
                    statusConfig.bgColor
                } ${
                    statusConfig.textColor
                } ${
                    statusConfig.borderColor
                }`}
            >
                {statusConfig.text}
            </span>
        </div>
    )
}

export default Status
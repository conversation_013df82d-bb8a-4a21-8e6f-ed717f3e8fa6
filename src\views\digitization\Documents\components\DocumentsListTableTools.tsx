import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbPrinter, TbFolderX, TbTrash } from 'react-icons/tb'

interface DocumentsListTableToolsProps {
    selectedCount?: number
    onPrintDocuments?: (fromDocument: number, toDocument: number) => void
    onArchiveDocuments?: (fromDocument: number, toDocument: number) => void
    onDeleteDocuments?: (fromDocument: number, toDocument: number) => void
}

const DocumentsListTableTools = ({
    selectedCount = 0,
    // onPrintDocuments,
    // onArchiveDocuments,
    // onDeleteDocuments,
}: DocumentsListTableToolsProps) => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // Implement search logic here
        console.log('Search:', value)
    }

    return (
        <div className="space-y-4 ">
            {/* Action Buttons Row */}
            {/* <div className="flex flex-wrap items-center gap-3">
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbPrinter />}
                    className="flex items-center gap-2"
                    onClick={() => console.log('Print documents')}
                >
                    {t('nav.documents.printDocuments')}
                </Button>
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbFolderX />}
                    className="flex items-center gap-2"
                    onClick={() => console.log('Archive documents')}
                >
                    {t('nav.documents.archiveDocuments')}
                </Button>
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbTrash />}
                    className="flex items-center gap-2 text-red-600 hover:text-red-700"
                    onClick={() => console.log('Delete documents')}
                >
                    {t('nav.shared.delete')}
                </Button>
                {selectedCount > 0 && (
                    <span className="text-sm text-gray-600 ml-2">
                        {selectedCount} {t('nav.shared.selected')}
                    </span>
                )}
            </div> */}

            {/* Search and Filter Row */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 ">
                {/* Left side - Search */}
                <div className="flex items-center gap-3 w-full">
                    <div className="relative w-full">
                        <Input
                            placeholder={t('nav.shared.search')}
                            value={searchTerm}
                            prefix={<TbSearch className="text-lg" />}
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>
                </div>

                {/* Right side - Filter and Column controls */}
                <div className="flex items-center gap-2">
                    {/* Future: Add filter and column controls here */}
                </div>
            </div>
        </div>
    )
}

export default DocumentsListTableTools

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { changeStatusBuilding } from '@/services/BuildingService'
import { Status } from '@/@types/common'

export const useChangeStatusBuilding = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: Status }) => 
      changeStatusBuilding(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] })
      queryClient.invalidateQueries({ queryKey: ['building', id] })
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to update building status')
    },
  })
}
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { linkFilesToBox } from '@/services/Boxes'

export const useLinkFilesToBox = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, { boxId: string | number; fileIds: string[] }>({
        mutationFn: ({ boxId, fileIds }) => linkFilesToBox(boxId, fileIds),
        onSuccess: (_, variables) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Invalidate the specific box query to refetch updated box details
            queryClient.invalidateQueries({ queryKey: ['box', variables.boxId] })
            // Optionally invalidate files queries if they exist
            queryClient.invalidateQueries({ queryKey: ['files'] })
        },
        onError: (error) => {
            console.error('Failed to link files to box:', error?.message || 'Unknown error')
        },
    })
}

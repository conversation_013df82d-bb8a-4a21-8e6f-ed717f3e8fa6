/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { jwtDecode } from 'jwt-decode'

interface AuthState {
    setAuth: (token: string, refreshToken: string) => void
    logout: () => void
}

export const useAuthStore = create<AuthState>()(() => ({
    setAuth: (token, refreshToken) => {
        localStorage.setItem('token', token)
        localStorage.setItem('refreshToken', refreshToken)
        const decoded: any = jwtDecode(token)
        localStorage.setItem('roles', decoded.roles.join(','))
        window.location.href = '/'
    },

    logout: () => {
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('roles')
        localStorage.removeItem('selectedOrganization')
        localStorage.removeItem('org_code')
        window.location.href = '/'
    },
}))

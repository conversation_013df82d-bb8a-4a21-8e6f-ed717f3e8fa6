/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createWarehouse } from '@/services/Warehouses'
import type { WarehouseForm } from '@/@types/warehouse'

export const useCreateWarehouse = () => {
    const queryClient = useQueryClient()

    return useMutation<any, Error, WarehouseForm>({
        mutationFn: createWarehouse,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
        },
        onError: (error) => {
            console.log('Error creating warehouse:', error)
        },
    })
}

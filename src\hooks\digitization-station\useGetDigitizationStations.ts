import { useQuery } from '@tanstack/react-query'
import { getDigitizationStations } from '@/services/DigitizationStationService'
import type { DigitizationStation } from '@/@types/digitizationStation'

export const useGetDigitizationStations = () => {
  return useQuery<DigitizationStation[]>({
    queryKey: ['digitization-stations'],
    queryFn: getDigitizationStations,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
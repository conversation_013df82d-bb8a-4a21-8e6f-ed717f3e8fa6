import { useQuery } from '@tanstack/react-query'
import { getMyDigitizationStations } from '@/services/DigitizationStationService'
import type { DigitizationStationResponse } from '@/@types/digitizationStation'

export const useGetMyDigitizationStations = () => {
  return useQuery<DigitizationStationResponse[]>({
    queryKey: ['digitization-stations', 'me'],
    queryFn: getMyDigitizationStations,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import WarehouseDropdown from './WarehouseDropdown'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbTruck } from 'react-icons/tb'

interface TransferBoxesTableToolsProps {
    selectedCount?: number
    selectedBoxIds?: string[]
    onTransferBoxes?: (warehouseId: string, boxIds: string[], notes: string) => void
}

const TransferBoxesTableTools = ({
    selectedCount = 0,
    selectedBoxIds = [],
    onTransferBoxes,
}: TransferBoxesTableToolsProps) => {
    const { t } = useTranslation()
    const [selectedWarehouseId, setSelectedWarehouseId] = useState<string>('')
    const [notes, setNotes] = useState<string>('')

    const handleTransferBoxes = () => {
        if (selectedWarehouseId && selectedBoxIds.length > 0 && onTransferBoxes) {
            onTransferBoxes(selectedWarehouseId, selectedBoxIds, notes)
            // Reset form after successful transfer
            setSelectedWarehouseId('')
            setNotes('')
        }
    }

    return (
        <div className="space-y-4 flex flex-col justify-between gap-2">
            {/* Action Buttons Row */}
            <div className="flex flex-wrap items-center gap-3">
                <WarehouseDropdown
                    value={selectedWarehouseId}
                    placeholder={t('nav.warehouses.selectWarehouse')}
                    onChange={setSelectedWarehouseId}
                />
                {selectedCount > 0 && (
                    <>
                        <Input
                            placeholder={t('nav.transferBoxes.transferNotes')}
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            className="min-w-[200px]"
                        />
                        <Button
                            variant="default"
                            size="sm"
                            icon={<TbTruck />}
                            className="flex items-center gap-2"
                            disabled={!selectedWarehouseId}
                            onClick={handleTransferBoxes}
                        >
                            {t('nav.transferBoxes.transferToWarehouse')}
                        </Button>
                        <span className="text-sm text-gray-600 ml-2">
                            {selectedCount} {t('nav.shared.selected')}
                        </span>
                    </>
                )}
            </div>

            {/* Search Row */}
            <div className="flex flex-wrap items-center gap-3">
                <div className="relative">
                    <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                        placeholder={t('nav.shared.search')}
                        className="pl-10 min-w-[300px]"
                    />
                </div>
            </div>
        </div>
    )
}

export default TransferBoxesTableTools

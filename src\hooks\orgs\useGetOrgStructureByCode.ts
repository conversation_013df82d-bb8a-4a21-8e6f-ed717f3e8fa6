import { getOrganizeStructureByCode } from '@/services/OrganizeStructureService'
import { useQuery } from '@tanstack/react-query'

export const useGetOrgStructureByCode = (code: string) => {
    return useQuery({
        queryKey: ['orgStructure', code],
        queryFn: () => getOrganizeStructureByCode(code),
        enabled: !!code,
        staleTime: 1000 * 60 * 5,
        gcTime: 1000 * 60 * 10,
        retry: 2,
    })
}
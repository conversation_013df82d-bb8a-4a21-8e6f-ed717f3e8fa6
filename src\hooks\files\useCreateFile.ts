import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createFile } from '@/services/FilesService'

export const useCreateFile = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: createFile,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['files'] })
        },
        onError: (error) => {
            console.log('Error creating file:', error)
        },
    })
}

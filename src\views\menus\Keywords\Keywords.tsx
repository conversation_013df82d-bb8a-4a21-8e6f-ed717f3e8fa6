import React, { useState } from 'react'
import { AdaptiveCard, Container } from '@/components/shared'
import { useGetKeywords, useDeleteMultipleKeywords, useKeywordSearch } from '@/hooks/keywords'
import KeywordHeader from './components/KeywordHeader'
import KeywordActionTools from './components/KeywordActionTools'
import KeywordsTable from './components/KeywordsTable'
import KeywordDialog from './components/KeywordDialog'
import KeywordSelectionFooter from './components/KeywordSelectionFooter'
import { TbBuilding } from 'react-icons/tb'
import CountBadges from '@/components/shared/displaying/CountBadges'
import useTranslation from '@/utils/hooks/useTranslation'

const Keywords = () => {
    const { t } = useTranslation()
    const { data: keywords = [], isLoading, isError } = useGetKeywords()
    const { results: searchResults, isLoading: isSearchLoading, search } = useKeywordSearch()

    const deleteMultipleKeywordsMutation = useDeleteMultipleKeywords()
    const [dialogOpen, setDialogOpen] = useState(false)
    const [editingKeywordId, setEditingKeywordId] = useState<number | null>(
        null,
    )
    const [visibleColumns, setVisibleColumns] = useState([
        'id',
        'value',
        'numberOfTimesUsed',
        'actions',
    ])
    const [selectedKeywords, setSelectedKeywords] = useState<number[]>([])
    const [searchTerm, setSearchTerm] = useState('')

    const handleAddKeyword = () => {
        setEditingKeywordId(null)
        setDialogOpen(true)
    }

    const handleEditKeyword = (keywordId: number) => {
        setEditingKeywordId(keywordId)
        setDialogOpen(true)
    }

    const handleCloseDialog = () => {
        setDialogOpen(false)
        setEditingKeywordId(null)
    }

    const handleDeleteSelected = async () => {
        if (selectedKeywords.length > 0) {
            await deleteMultipleKeywordsMutation.mutateAsync(selectedKeywords)
            setSelectedKeywords([])
        }
    }

    const handleClearSelection = () => {
        setSelectedKeywords([])
    }

    const handleSearch = (term: string) => {
        setSearchTerm(term)
        setSelectedKeywords([]) // Clear selection when switching between search and regular data
        if (term.trim()) {
            search(term)
        }
    }

    // Determine which data to display
    const displayData = searchTerm.trim() ? searchResults.map(result => ({
        id: parseInt(result.id),
        value: result.value,
        numberOfTimesUsed: 0, // Search results don't have usage count
    })) : keywords

    const displayLoading = searchTerm.trim() ? isSearchLoading : isLoading

    return (
        <Container className="h-full">
            <AdaptiveCard className="shadow-lg">
                <div className="flex flex-col gap-6">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex justify-between items-center gap-3 w-full">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbBuilding className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                    {t('nav.buildings.buildings')}
                                </h3>
                            </div>
                            <CountBadges
                                counts={{
                                    total: displayData.length,
                                }}
                                loading={displayLoading}
                                error={isError}
                            />
                        </div>
                        <KeywordHeader onAddKeyword={handleAddKeyword} />
                    </div>

                    {/* Action Tools */}
                    <div className="">
                        <KeywordActionTools
                            onColumnVisibilityChange={setVisibleColumns}
                            onSearch={handleSearch}
                        />
                    </div>

                    {/* Table */}
                    <div className="">
                        <KeywordsTable
                            visibleColumns={visibleColumns}
                            selectedKeywords={selectedKeywords}
                            keywords={displayData}
                            isLoading={displayLoading}
                            onEdit={handleEditKeyword}
                            onSelectionChange={setSelectedKeywords}
                        />
                    </div>

                    {/* Dialog */}
                    <KeywordDialog
                        isOpen={dialogOpen}
                        keywordId={editingKeywordId}
                        onClose={handleCloseDialog}
                    />

                    {/* Selection Footer */}
                    <KeywordSelectionFooter
                        selectedCount={selectedKeywords.length}
                        onDeleteSelected={handleDeleteSelected}
                        onClearSelection={handleClearSelection}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )
}

export default Keywords

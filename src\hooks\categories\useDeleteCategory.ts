/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteCategory } from '@/services/CategoriesService'

export const useDeleteCategory = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, string | number>({
    mutationFn: deleteCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] })
    },
    onError: (error) => {
      console.log('Error deleting category:', error)
    },
  })
}
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteGeneralCategory } from '@/services/GeneralCategoriesService'

export const useDeleteGeneralCategory = () => {
    const queryClient = useQueryClient()
    return useMutation({
        mutationFn: deleteGeneralCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
        },
        onError: (error) => {
            console.log('Error deleting general category:', error)
        },
    })
}

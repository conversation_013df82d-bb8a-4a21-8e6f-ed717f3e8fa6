import { useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import LinkFilesActionTools from './components/LinkFilesActionTools'
import LinkFilesTableTools from './components/LinkFilesTableTools'
import LinkFilesTable from './components/LinkFilesTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbLink } from 'react-icons/tb'
import { useLinkFilesToBox } from '@/hooks/boxes'

const LinkFiles = () => {
    const { t } = useTranslation()
    const [selectedFileIds, setSelectedFileIds] = useState<(number | string)[]>(
        [],
    )

    const { mutate: linkFiles, isSuccess } = useLinkFilesToBox()

    const handleLinkFiles = (boxId: string, fileIds: string[]) => {
        linkFiles({ boxId, fileIds })
        if (isSuccess) {
            setSelectedFileIds([])
        }
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbLink className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="">
                                    {t('nav.linkFiles.linkFiles')}
                                </h3>
                            </div>
                            <LinkFilesActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <LinkFilesTableTools
                                selectedCount={selectedFileIds.length}
                                selectedFileIds={selectedFileIds.map((id) =>
                                    String(id),
                                )}
                                onLinkFiles={handleLinkFiles}
                            />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <LinkFilesTable
                                selectedFileIds={selectedFileIds}
                                onSelectionChange={setSelectedFileIds}
                            />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>
        </>
    )
}

export default LinkFiles

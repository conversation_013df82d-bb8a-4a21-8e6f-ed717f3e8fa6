/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { transferBoxesToWarehouseApi } from '@/services/Warehouses'

export const useTransferBoxesToWarehouse = () => {
    const queryClient = useQueryClient()

    return useMutation<
        void,
        Error,
        { warehouseId: string; boxes: string[]; notes: string }
    >({
        mutationFn: ({ warehouseId, boxes, notes }) =>
            transferBoxesToWarehouseApi(warehouseId, { boxes, notes }),
        onSuccess: (data, variables) => {
            // Invalidate warehouse-related queries to refresh data
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
            queryClient.invalidateQueries({
                queryKey: ['warehouse', variables.warehouseId],
            })
            queryClient.invalidateQueries({ queryKey: ['boxes'] })

            console.log('Transfer request successful:', data)
        },
        onError: (error) => {
            console.log('Error transferring boxes to warehouse:', error)
        },
    })
}

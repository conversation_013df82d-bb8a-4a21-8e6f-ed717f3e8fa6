import React, { useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import { Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, TbTrash } from 'react-icons/tb'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'
import { Document } from '@/@types/document'
import { ConfirmDialog } from '@/components/shared'
import { useDeleteDocument } from '@/hooks/documents'
import PrintBarCode from '@/components/shared/displaying/PrintBarCode'

interface ActionsProps {
    documentData: Document
}

export default function Actions({ documentData }: ActionsProps) {
    const navigate = useNavigate()
    const { t } = useTranslation()
    const { fileId } = useParams()

    const [print, setPrint] = useState(false)

    const [deleteConfirmation, setDeleteConfirmation] = useState(false)

    const { mutate: deleteDocument } = useDeleteDocument()

    const handleConfirmDelete = () => {
        deleteDocument(documentData.documentId)
        setDeleteConfirmation(false)
    }

    const handleCancelDelete = () => {
        setDeleteConfirmation(false)
    }

    const handleReview = () => {
        navigate(
            `/digitization/${fileId}/docs-review/${documentData.documentId}`,
        )
    }

    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.review')}>
                <div
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={handleReview}
                >
                    <TbEye className="text-lg" />
                </div>
            </Tooltip>

            <Tooltip title={t('nav.shared.print')}>
                <div
                    className="text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={() => {
                        setPrint(true)
                    }}
                >
                    <TbPrinter className="text-lg" />
                </div>
            </Tooltip>

            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={() => setDeleteConfirmation(true)}
                >
                    <TbTrash className="text-lg" />
                </div>
            </Tooltip>

            {print && (
                <PrintBarCode
                    fileId={documentData.documentId}
                    onClose={() => setPrint(false)}
                />
            )}

            <ConfirmDialog
                isOpen={deleteConfirmation}
                type="danger"
                title={t('nav.shared.deleteConfirmation')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={handleCancelDelete}
                onRequestClose={handleCancelDelete}
                onCancel={handleCancelDelete}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.deleteConfirmationMessage', {
                        item: documentData.title || 'document',
                    })}
                </p>
            </ConfirmDialog>
        </div>
    )
}

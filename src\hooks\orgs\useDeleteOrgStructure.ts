import { deleteOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'

export const useDeleteOrgStructure = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: (code: string) => deleteOrganizeStructure(code),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
            queryClient.invalidateQueries({ queryKey: ['orgStructure'] })
            
        },
        onError: () => {
            
        },
    })
}
import { useMutation } from '@tanstack/react-query'
import { refreshTokenApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'

export function useRefreshToken() {
    const { logout } = useAuthStore()

    return useMutation<void, Error, void>({
        mutationFn: refreshTokenApi,
        onSuccess: () => {},
        onError: (error) => {
            console.error('Error refreshing token:', error)
            logout()
        },
    })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createBuilding } from '@/services/BuildingService'
import { BuildingForm } from '@/@types/building'

export const useCreateBuilding = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (building: BuildingForm) => createBuilding(building),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] })
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to create building')
    },
  })
}
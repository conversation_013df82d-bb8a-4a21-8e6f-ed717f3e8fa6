/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWarehouseShelf } from '@/services/Warehouses'

export const useDeleteWarehouseShelf = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        { warehouseId: string; areaId: string; unitId: string; shelfId: string }
    >({
        mutationFn: ({ warehouseId, areaId, unitId, shelfId }) =>
            deleteWarehouseShelf(warehouseId, areaId, unitId, shelfId),
        onSuccess: (_, { warehouseId, areaId, unitId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: [
                    'warehouse',
                    warehouseId,
                    'area',
                    areaId,
                    'unit',
                    unitId,
                ],
            })
        },
        onError: (error) => {
            console.log('Error deleting warehouse shelf:', error)
        },
    })
}

import { getOrganizeStructure } from '@/services/OrganizeStructureService'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { RootNodes } from '@/@types/organizationalStructure'

// Interface for nested nodes optimized for tree components
export interface NestedNode {
    code: string
    name: string
    description: string
    level: number
    childrenCount: number
    parentCode?: string
    children?: NestedNode[]
}

// Utility function to flatten organizational structure
const flattenOrganizationalNodes = (nodes: RootNodes[]): RootNodes[] => {
    const result: RootNodes[] = []

    const flatten = (node: RootNodes, parentCode?: string) => {
        // Add current node with flattened structure (without children to avoid circular references)
        result.push({
            code: node.code,
            name: node.name,
            description: node.description,
            level: node.level,
            childrenCount: node.childrenCount || 0,
            parentCode: parentCode || node.parentCode,
        })

        // Recursively flatten children if they exist
        if (node.children && node.children.length > 0) {
            node.children.forEach((child) => flatten(child, node.code))
        }
    }

    nodes.forEach((node) => flatten(node))
    return result
}

// Utility function to build nested tree structure optimized for tree components
const buildNestedNodes = (nodes: RootNodes[]): NestedNode[] => {
    if (!Array.isArray(nodes)) return []

    return nodes.map((node) => {
        const nestedNode: NestedNode = {
            code: node.code,
            name: node.name,
            description: node.description,
            level: node.level,
            childrenCount: node.childrenCount || 0,
            parentCode: node.parentCode,
            children: node.children ? buildNestedNodes(node.children) : [],
        }
        return nestedNode
    })
}

export const useGetOrgsStructure = () => {
    const query = useQuery({
        queryKey: ['orgsStructure'],
        queryFn: getOrganizeStructure,
        staleTime: 1000 * 60 * 5,
        gcTime: 1000 * 60 * 10,
        retry: 2,
    })

    // Memoize flattened nodes to avoid unnecessary recalculations
    const flattenedNodes = useMemo(() => {
        if (!query.data?.rootNodes) return []
        return flattenOrganizationalNodes(query.data.rootNodes)
    }, [query.data?.rootNodes])

    // Memoize nested nodes to avoid unnecessary recalculations
    const nestedNodes = useMemo(() => {
        if (!query.data?.rootNodes) return []
        return buildNestedNodes(query.data.rootNodes)
    }, [query.data?.rootNodes])

    return {
        ...query,
        flattenedNodes,
        nestedNodes,
        // Keep original data structure for backward compatibility
        data: query.data,
        // Convenience accessors
        rootNodes: query.data?.rootNodes || [],
        levelCounts: query.data?.levelCounts,
        totalNodes: query.data?.totalNodes,
    }
}

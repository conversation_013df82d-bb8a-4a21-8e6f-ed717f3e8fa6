import { useQuery } from '@tanstack/react-query'
import { getGeneralCategoryById } from '@/services/GeneralCategoriesService'

export const useGetGeneralCategoryById = (id: number) => {
    return useQuery({
        queryKey: ['generalCategory', id],
        queryFn: () => getGeneralCategoryById(id),
        enabled: !!id,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

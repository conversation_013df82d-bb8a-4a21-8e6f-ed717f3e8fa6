/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWarehouseArea } from '@/services/Warehouses'

export const useDeleteWarehouseArea = () => {
    const queryClient = useQueryClient()

    return useMutation<any, Error, { warehouseId: string; areaId: string }>({
        mutationFn: ({ warehouseId, areaId }) =>
            deleteWarehouseArea(warehouseId, areaId),
        onSuccess: (_, { warehouseId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
        },
        onError: (error) => {
            console.log('Error deleting warehouse area:', error)
        },
    })
}

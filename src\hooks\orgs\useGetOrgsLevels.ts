import { OrgLevel } from '@/@types/organizationalStructure'
import { getOrganizationsByLevels } from '@/services/OrganizeStructureService'
import { useQuery } from '@tanstack/react-query'

export const useGetOrgsLevels = (level: OrgLevel) => {
    return useQuery({
        queryKey: ['orgsLevels', level],
        queryFn: () => getOrganizationsByLevels(level),
        enabled: !!level,
        staleTime: 1000 * 60 * 5,
        gcTime: 1000 * 60 * 10,
        retry: 2,
    })
}

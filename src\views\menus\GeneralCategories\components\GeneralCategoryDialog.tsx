import React, { useEffect } from 'react'
import Dialog from '@/components/ui/Dialog'
import { Form, FormContainer, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import {
    useGetGeneralCategories,
    useCreateGeneralCategory,
    useUpdateGeneralCategory,
} from '@/hooks/genralCategories'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import useTranslation from '@/utils/hooks/useTranslation'

interface GeneralCategoryDialogProps {
    isOpen: boolean
    generalCategoryId?: number | null
    onClose: () => void
}

type FormData = {
    name: string
}

const GeneralCategoryDialog = ({
    isOpen,
    generalCategoryId,
    onClose,
}: GeneralCategoryDialogProps) => {
    const { t } = useTranslation()
    const { data: generalCategories = [] } = useGetGeneralCategories()
    const createGeneralCategoryMutation = useCreateGeneralCategory()
    const updateGeneralCategoryMutation = useUpdateGeneralCategory()

    const schema = z.object({
        name: z
            .string()
            .min(1, t('nav.generalCategories.nameRequired'))
            .max(100, t('nav.generalCategories.nameTooLong')),
    })

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            name: '',
        },
    })

    // Helper function to find general category by ID
    const getGeneralCategoryById = (id: number) => {
        return generalCategories.find((category) => category.id === id)
    }

    // Load general category data when editing
    useEffect(() => {
        if (generalCategoryId && isOpen) {
            const generalCategory = getGeneralCategoryById(generalCategoryId)
            if (generalCategory) {
                setValue('name', generalCategory.name)
            }
        } else if (isOpen) {
            reset({ name: '' })
        }
    }, [generalCategoryId, isOpen, generalCategories, setValue, reset])

    const onSubmit = async (data: FormData) => {
        try {
            if (generalCategoryId) {
                // Update existing general category
                await updateGeneralCategoryMutation.mutateAsync({
                    id: generalCategoryId,
                    name: data.name,
                })
            } else {
                // Create new general category
                await createGeneralCategoryMutation.mutateAsync(data.name)
            }
            onClose()
            reset()
        } catch (error) {
            console.error('Error saving general category:', error)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    const isLoading =
        createGeneralCategoryMutation.isPending ||
        updateGeneralCategoryMutation.isPending

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full justify-between">
                <div className="overflow-y-auto">
                    <div className="px-6 py-4">
                        <h4 className="mb-6">
                            {generalCategoryId
                                ? t('nav.generalCategories.editGeneralCategory')
                                : t('nav.generalCategories.addGeneralCategory')}
                        </h4>
                    </div>

                    <Form onSubmit={handleSubmit(onSubmit)}>
                        <FormContainer>
                            <FormItem
                                label={t('nav.generalCategories.name')}
                                invalid={!!errors.name}
                                errorMessage={errors.name?.message}
                            >
                                <Input
                                    {...register('name')}
                                    placeholder={t(
                                        'nav.generalCategories.enterName',
                                    )}
                                    autoFocus
                                />
                            </FormItem>

                            <div className="flex justify-end gap-2 mt-6">
                                <Button
                                    type="button"
                                    variant="plain"
                                    onClick={handleClose}
                                    disabled={isLoading}
                                >
                                    {t('nav.shared.cancel')}
                                </Button>
                                <Button
                                    type="submit"
                                    variant="solid"
                                    loading={isLoading}
                                    disabled={isLoading}
                                >
                                    {generalCategoryId
                                        ? t('nav.shared.update')
                                        : t('nav.shared.create')}
                                </Button>
                            </div>
                        </FormContainer>
                    </Form>
                </div>
            </div>
        </Dialog>
    )
}

export default GeneralCategoryDialog

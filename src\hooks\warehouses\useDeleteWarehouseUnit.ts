/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteWarehouseUnit } from '@/services/Warehouses'

export const useDeleteWarehouseUnit = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        { warehouseId: string; areaId: string; unitId: string }
    >({
        mutationFn: ({ warehouseId, areaId, unitId }) =>
            deleteWarehouseUnit(warehouseId, areaId, unitId),
        onSuccess: (_, { warehouseId, areaId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId, 'area', areaId],
            })
        },
        onError: (error) => {
            console.log('Error deleting warehouse unit:', error)
        },
    })
}

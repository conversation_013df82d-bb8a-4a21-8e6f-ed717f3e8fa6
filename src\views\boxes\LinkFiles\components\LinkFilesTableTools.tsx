import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import BoxDropdown from './BoxDropdown'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbLink } from 'react-icons/tb'

interface LinkFilesTableToolsProps {
    selectedCount?: number
    selectedFileIds?: string[]
    onLinkFiles?: (boxId: string, fileIds: string[]) => void
}

const LinkFilesTableTools = ({
    selectedCount = 0,
    selectedFileIds = [],
    onLinkFiles,
}: LinkFilesTableToolsProps) => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedBoxId, setSelectedBoxId] = useState<string>('')

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // Implement search logic here
        console.log('Search:', value)
    }

    const handleLinkFiles = () => {
        if (selectedBoxId && selectedFileIds.length > 0) {
            onLinkFiles?.(selectedBoxId, selectedFileIds)
        }
    }

    return (
        <div className="space-y-4 flex flex-col  justify-between gap-2">
            {/* Action Buttons Row */}
            <div className="flex flex-wrap items-center gap-3">
                <BoxDropdown
                    value={selectedBoxId}
                    placeholder="Boxes"
                    onChange={setSelectedBoxId}
                />
                {selectedCount > 0 && (
                    <>
                        <Button
                            variant="default"
                            size="sm"
                            icon={<TbLink />}
                            className="flex items-center gap-2"
                            disabled={!selectedBoxId}
                            onClick={handleLinkFiles}
                        >
                            {t('nav.linkFiles.linkToBox')}
                        </Button>
                        <span className="text-sm text-gray-600 ml-2">
                            {selectedCount} {t('nav.shared.selected')}
                        </span>
                    </>
                )}
            </div>

            {/* Search Row */}
            <div className="flex-1">
                <Input
                    placeholder={t('nav.files.searchByFileTitle')}
                    value={searchTerm}
                    prefix={<TbSearch className="text-lg" />}
                    className="w-full"
                    onChange={(e) => handleSearch(e.target.value)}
                />
            </div>
        </div>
    )
}

export default LinkFilesTableTools

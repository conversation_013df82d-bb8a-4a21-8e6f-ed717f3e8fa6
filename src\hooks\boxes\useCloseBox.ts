import { useMutation, useQueryClient } from '@tanstack/react-query'
import { closeBox } from '@/services/Boxes'

export const useCloseBox = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, { boxId: string | number }>({
        mutationFn: ({ boxId }) => closeBox(boxId),
        onSuccess: (_, variables) => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
            // Invalidate the specific box query to refetch updated box details
            queryClient.invalidateQueries({
                queryKey: ['box', variables.boxId],
            })
        },
        onError: (error) => {
            console.error(
                'Failed to close box:',
                error?.message || 'Unknown error',
            )
        },
    })
}

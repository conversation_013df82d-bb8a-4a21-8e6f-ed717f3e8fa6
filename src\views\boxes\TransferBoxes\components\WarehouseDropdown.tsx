import Select from '@/components/ui/Select'
import { useGetWarehouses } from '@/hooks/warehouses'
import useTranslation from '@/utils/hooks/useTranslation'
import type { Warehouse } from '@/@types/warehouse'

interface WarehouseDropdownProps {
    value: string
    onChange: (value: string) => void
    placeholder?: string
}

const WarehouseDropdown = ({
    value,
    onChange,
    placeholder,
}: WarehouseDropdownProps) => {
    const { t } = useTranslation()

    // Fetch all warehouses
    const { data: warehouses, isLoading } = useGetWarehouses()

    // Create options with warehouse name as main label and code in parentheses
    const options = [
        {
            value: '',
            label: t('common.select'),
        },
        ...(warehouses?.map((warehouse: Warehouse) => ({
            value: warehouse.id,
            label: `${warehouse.name} (${warehouse.id})`,
        })) || []),
    ]

    const selectedOption = options?.find((option) => option.value === value)

    return (
        <Select
            options={options}
            value={selectedOption}
            placeholder={placeholder || t('nav.warehouses.selectWarehouse')}
            isLoading={isLoading}
            className="min-w-[250px]"
            onChange={(option) => onChange(option?.value || '')}
        />
    )
}

export default WarehouseDropdown

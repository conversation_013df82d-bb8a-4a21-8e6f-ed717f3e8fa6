/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createCategory } from '@/services/CategoriesService'
import type { Category } from '@/@types/categories'

export const useCreateCategory = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, Omit<Category, 'id'>>({
    mutationFn: createCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] })
    },
    onError: (error) => {
      console.log('Error creating category:', error)
    },
  })
}
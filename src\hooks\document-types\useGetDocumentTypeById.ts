import { useQuery } from '@tanstack/react-query'
import { getDocumentTypeById } from '@/services/DocumentTypesService'
import type { DocumentType } from '@/services/DocumentTypesService'

export const useGetDocumentTypeById = (id?: number) => {
    return useQuery<DocumentType>({
        queryKey: ['document-type', id],
        queryFn: () => getDocumentTypeById(id!),
        enabled: !!id,
        staleTime: 10 * 60 * 1000, // 10 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
        retry: 3,
    })
}

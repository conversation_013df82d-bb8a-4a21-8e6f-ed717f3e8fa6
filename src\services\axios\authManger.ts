// src/lib/authManager.ts

import ApiService from '../ApiService'

let refreshTimeout: NodeJS.Timeout | null = null

export function scheduleTokenRefresh(expiresIn: number) {
    if (refreshTimeout) {
        clearTimeout(refreshTimeout)
    }

    const refreshBefore = (expiresIn - 60) * 1000

    refreshTimeout = setTimeout(async () => {
        try {
            const token = localStorage.getItem('token')
            const refreshToken = localStorage.getItem('refreshToken')

            if (!refreshToken) return

            const {
                data,
            }: {
                data: { token: string; refreshToken: string; expiresIn: number }
            } = await ApiService.post('/Auth/refresh-token', {
                token,
                refreshToken,
            })

            localStorage.setItem('token', data.token)
            localStorage.setItem('refreshToken', data.refreshToken)

            console.log('🔄 Token refreshed proactively')

            scheduleTokenRefresh(data.expiresIn)
        } catch (err) {
            console.error('❌ Failed to refresh token proactively', err)
        }
    }, refreshBefore)
}

export function clearTokenRefresh() {
    if (refreshTimeout) {
        clearTimeout(refreshTimeout)
        refreshTimeout = null
    }
}

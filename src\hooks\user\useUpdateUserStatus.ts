import { Status } from '@/@types/common'
import { updateUserStatusApi } from '@/services/UsersService'
import { useMutation, useQueryClient } from '@tanstack/react-query'

export const useUpdateUserStatus = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, { userId: string; status: Status }>({
        mutationFn: ({ userId, status }) => updateUserStatusApi(userId, status),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            console.log('User status updated successfully')
        },
        onError: (error) => {
            console.log('Error updating user status:', error)
        },
    })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateKeywordApi } from '@/services/Keywords'

export const useUpdateKeyword = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ id, value }: { id: number; value: string }) =>
            updateKeywordApi(id, value),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
        },
        onError: (error) => {
            console.error('Error updating keyword:', error)
        },
    })
}

export default useUpdateKeyword

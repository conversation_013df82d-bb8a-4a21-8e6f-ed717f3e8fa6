import { updateOrganizeStructure } from '@/services/OrganizeStructureService'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { UpdatedNode } from '@/@types/organizationalStructure'

export const useUpdateOrgStructure = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ code, data }: { code: string; data: UpdatedNode }) =>
            updateOrganizeStructure(code, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['orgsStructure'] })
            queryClient.invalidateQueries({ queryKey: ['orgStructure'] })
           
        },
        onError: () => {
            
        },
    })
}
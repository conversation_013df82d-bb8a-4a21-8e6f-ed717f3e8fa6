/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteFile } from '@/services/FilesService'

export const useDeleteFile = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, string>({
    mutationFn: deleteFile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] })
    },
    onError: (error) => {
      console.log('Error deleting file:', error)
    },
  })
}
import React, { useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import {
    TbFileBarcode,
    TbLock,
    Tb<PERSON>rin<PERSON>,
    TbTrash,
    // TbUsersPlus,
} from 'react-icons/tb'
import PrintBarCode from '@/components/shared/displaying/PrintBarCode'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { File } from '@/@types/file'
import { ConfirmDialog } from '@/components/shared'
import { useCloseFile, useDeleteFile } from '@/hooks/files'

export default function Actions({ fileData }: { fileData: File }) {
    const navigate = useNavigate()
    const { t } = useTranslation()

    const [deleteConfirmation, setDeleteConfirmation] = useState(false)
    const [print, setPrint] = useState(false)

    const { mutate: deleteFile } = useDeleteFile()
    const { mutate: closeFile } = useCloseFile()

    const handleConfirmDelete = () => {
        deleteFile(fileData.fileId)
        setDeleteConfirmation(false)
    }

    const handleCancelDelete = () => {
        setDeleteConfirmation(false)
    }

    return (
        <div className="flex items-center justify-center gap-3">
            {fileData.fileStatus === 0 && fileData.numberOfPages !== 0 && (
                <Tooltip title={t('nav.shared.lock')}>
                    <div
                        className="text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        role="button"
                        onClick={() => {
                            closeFile(fileData.fileId)
                        }}
                    >
                        <TbLock className="text-lg" />
                    </div>
                </Tooltip>
            )}
            <Tooltip title={t('nav.shared.view')}>
                <div
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={() => {
                        navigate(`/digitization/${fileData.fileId}/documents`)
                    }}
                >
                    <TbFileBarcode className="text-lg" />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.print')}>
                <div
                    className="text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                    role="button"
                    onClick={() => setPrint(true)}
                >
                    <TbPrinter className="text-lg" />
                </div>
            </Tooltip>
            {/* <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={() => {}}
                >
                    <TbUsersPlus />
                </div>
            </Tooltip> */}
            {fileData.numberOfPages === 0 && (
                <Tooltip title={t('nav.shared.delete')}>
                    <div
                        className="text-red-600 hover:text-red-700 hover:bg-red-50  rounded-lg transition-colors duration-200 cursor-pointer"
                        role="button"
                        onClick={() => {
                            setDeleteConfirmation(true)
                        }}
                    >
                        <TbTrash className="text-lg" />
                    </div>
                </Tooltip>
            )}

            {print && (
                <PrintBarCode
                    fileId={fileData.fileId}
                    onClose={() => setPrint(false)}
                />
            )}

            <ConfirmDialog
                isOpen={deleteConfirmation}
                type="danger"
                title={t('nav.shared.deleteConfirmation')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={handleCancelDelete}
                onRequestClose={handleCancelDelete}
                onCancel={handleCancelDelete}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.deleteConfirmationMessage', {
                        item: fileData.fileTitle,
                    })}
                </p>
            </ConfirmDialog>
        </div>
    )
}

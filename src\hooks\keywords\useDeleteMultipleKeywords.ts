import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteKeyword<PERSON>pi } from '@/services/Keywords'

export const useDeleteMultipleKeywords = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (ids: number[]) => {
            return Promise.all(ids.map((id) => deleteKeywordApi(id)))
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
        },
        onError: (error) => {
            console.error('Error deleting keywords:', error)
        },
    })
}

export default useDeleteMultipleKeywords

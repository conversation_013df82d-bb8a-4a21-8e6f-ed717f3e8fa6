import { useMutation } from '@tanstack/react-query'
import { changePasswordApi } from '@/services/AuthService'

interface ChangePasswordData {
    currentPassword: string
    newPassword: string
}

export function useChangePassword() {
    return useMutation<void, Error, ChangePasswordData>({
        mutationFn: (data: ChangePasswordData) => changePasswordApi(data),
        onSuccess: () => {
            console.log('Password changed successfully')
        },
        onError: (error) => {
            console.error('Error changing password:', error)
        },
    })
}

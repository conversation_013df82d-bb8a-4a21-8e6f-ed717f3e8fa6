import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDigitizationStationStatus } from '@/services/DigitizationStationService'
import type { Status } from '@/@types/common'

export const useUpdateDigitizationStationStatus = () => {
  const queryClient = useQueryClient()

  return useMutation<void, Error, { id: string | number; status: Status }>({
    mutationFn: ({ id, status }) => updateDigitizationStationStatus(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['digitization-stations'] })
      queryClient.invalidateQueries({ queryKey: ['digitization-station', id] })
    },
    onError: (error) => {
      console.log('Error updating digitization station status:', error)
    },
  })
}
import { useMutation } from '@tanstack/react-query'
import { viewDocument } from '@/services/DocumentsService'

export const useViewDocument = () => {
  return useMutation({
    mutationFn: (filePath: string) => viewDocument(filePath),
    onSuccess: (blob: Blob, filePath: string) => {
      // Create a URL for the blob and trigger download/view
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filePath.split('/').pop() || 'document'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to view document')
    },
  })
}

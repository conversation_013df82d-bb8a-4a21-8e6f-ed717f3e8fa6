/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDigitizationStation } from '@/services/DigitizationStationService'
import type { DigitizationStationRequest } from '@/@types/digitizationStation'

export const useCreateDigitizationStation = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, DigitizationStationRequest>({
    mutationFn: createDigitizationStation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['digitization-stations'] })
    },
    onError: (error) => {
      console.log('Error creating digitization station:', error)
    },
  })
}
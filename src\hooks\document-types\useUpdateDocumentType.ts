import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocumentType } from '@/services/DocumentTypesService'
import type { DocumentType } from '@/services/DocumentTypesService'

export const useUpdateDocumentType = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({
            id,
            documentType,
        }: {
            id: number
            documentType: DocumentType
        }) => updateDocumentType(id, documentType),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
            queryClient.invalidateQueries({ queryKey: ['document-type', id] })
        },
        onError: (error) => {
            console.log('Error updating document type:', error)
        },
    })
}

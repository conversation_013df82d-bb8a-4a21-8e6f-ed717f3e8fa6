/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateCategory } from '@/services/CategoriesService'
import type { Category } from '@/@types/categories'

export const useUpdateCategory = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, { id: string | number; category: Partial<Category> }>({
    mutationFn: ({ id, category }) => updateCategory(id, category),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['categories'] })
      queryClient.invalidateQueries({ queryKey: ['category', id] })
    },
    onError: (error) => {
      console.log('Error updating category:', error)
    },
  })
}
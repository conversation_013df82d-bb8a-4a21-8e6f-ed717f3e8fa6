import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouse } from '@/services/Warehouses'
import type { WarehouseForm, Warehouse } from '@/@types/warehouse'

export const useUpdateWarehouse = () => {
  const queryClient = useQueryClient()

  return useMutation<Warehouse, Error, { id: string; warehouse: WarehouseForm }>({
    mutationFn: ({ id, warehouse }) => updateWarehouse(id, warehouse),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['warehouses'] })
      queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
    },
    onError: (error) => {
      console.log('Error updating warehouse:', error)
    },
  })
}
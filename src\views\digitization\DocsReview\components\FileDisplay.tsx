import React, { useState, useRef } from 'react'
import { <PERSON><PERSON>, Card, Input } from '@/components/ui'
import { TbScan, TbFileUpload, TbDeviceFloppy } from 'react-icons/tb'
import { useUploadDocumentPdf } from '@/hooks/documents'

interface FileDisplayProps {
    documentUrl?: string
    documentTitle?: string
    documentType?: string
    documentId?: string
    onFileUpload?: (file: File) => void
    onSave?: () => void
}

const FileDisplay: React.FC<FileDisplayProps> = ({
    documentUrl,
    documentTitle = 'Document Viewer',
    documentId,
    onFileUpload,
    onSave,
}) => {
    const [uploadedFile, setUploadedFile] = useState<{
        file: File
        url: string
        name: string
        type: string
    } | null>(null)

    const fileInputRef = useRef<HTMLInputElement>(null)
    const uploadMutation = useUploadDocumentPdf()

    const validatePdfFile = (file: File): boolean => {
        if (file.type !== 'application/pdf') {
            console.log('Please select a PDF file only')
            return false
        }
        if (file.size > 10 * 1024 * 1024) {
            console.log('File size must be less than 10MB')
            return false
        }
        return true
    }

    const handleFilePreview = async (file: File) => {
        if (!validatePdfFile(file)) return

        try {
            const previewUrl = URL.createObjectURL(file)
            setUploadedFile({
                file,
                url: previewUrl,
                name: file.name,
                type: file.type,
            })

            if (onFileUpload) {
                onFileUpload(file)
            }

            console.log('File selected and ready for upload')
        } catch (error) {
            console.error('File selection failed:', error)
            console.log('File selection failed. Please try again.')
        }
    }

    const handleServerUpload = async () => {
        if (!uploadedFile || !documentId) {
            console.log('Missing file or document ID')
            return
        }

        try {
            await uploadMutation.mutateAsync({
                documentId,
                pdfFile: uploadedFile.file,
            })
            console.log('File uploaded successfully to server')
        } catch (error) {
            console.error('Upload failed:', error)
            console.log('Upload failed. Please try again.')
            if (uploadedFile?.url) {
                URL.revokeObjectURL(uploadedFile.url)
                setUploadedFile(null)
            }
        }
    }

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            handleFilePreview(file)
        }
        if (event.target) {
            event.target.value = ''
        }
    }

    const handleAddPdfClick = () => {
        fileInputRef.current?.click()
    }

    const handleScanClick = () => {
        // TODO: Implement scan functionality
        console.log('Scan functionality will be implemented soon')
    }

    const handleSaveClick = async () => {
        // First upload to server
        await handleServerUpload()

        // Then call the onSave callback if provided
        if (onSave) {
            onSave()
        }
    }

    const displayUrl = uploadedFile?.url || documentUrl
    const displayTitle = uploadedFile?.name || documentTitle

    return (
        <Card className="h-full flex flex-col">
            <div className="p-4 border-b">
                <h3 className="text-lg font-semibold mb-4">{displayTitle}</h3>

                {/* Action Buttons */}
                <div className="flex flex-col gap-3 mb-4">
                    <Button
                        variant="solid"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={handleScanClick}
                    >
                        <TbScan className="text-lg" />
                        Scan
                    </Button>

                    <Button
                        variant="solid"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={handleAddPdfClick}
                    >
                        <TbFileUpload className="text-lg" />
                        Add PDF from PC
                    </Button>

                    {/* Save Button - Only show after file upload */}
                    {uploadedFile && (
                        <Button
                            variant="solid"
                            size="sm"
                            className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                            onClick={handleSaveClick}
                            loading={uploadMutation.isPending}
                            disabled={!documentId}
                        >
                            <TbDeviceFloppy className="text-lg" />
                            {uploadMutation.isPending
                                ? 'Uploading...'
                                : 'Save Document'}
                        </Button>
                    )}
                </div>

                {/* Hidden File Input */}
                <Input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,application/pdf"
                    onChange={handleFileSelect}
                    className="hidden"
                />
            </div>

            {/* Display Area */}
            <div className="flex-1 p-4">
                {displayUrl ? (
                    <div className="h-full">
                        <iframe
                            src={displayUrl}
                            className="w-full h-full border rounded-lg"
                            title={displayTitle}
                            style={{ minHeight: '500px' }}
                        />
                    </div>
                ) : (
                    <div className="h-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                        <div className="text-center text-gray-500">
                            <p className="text-lg mb-2">
                                No document to display
                            </p>
                            <p className="text-sm">
                                Use the buttons above to scan or add a PDF file
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    )
}

export default FileDisplay

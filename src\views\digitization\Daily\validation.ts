import { z } from 'zod'
import { Confidentiality, DigitizationType, MediumType } from '@/@types/file'
import type { FileForm } from '@/@types/file'

// Partial FileForm type for form input (excluding fields from localStorage)
export type PartialFileForm = Omit<
    FileForm,
    'digitizationStationId' | 'organizationalNodeId'
> & {
    userIdsWithAccess: string[]
}

// Validation schema for file creation form
export const fileCreationValidationSchema = z.object({
    fileTitle: z
        .string()
        .min(1, { message: 'File title is required' })
        .min(3, { message: 'File title must be at least 3 characters' })
        .max(100, { message: 'File title must not exceed 100 characters' }),
    confidentiality: z.nativeEnum(Confidentiality, {
        message: 'Confidentiality level is required',
    }),
    categoryId: z.number().min(1, { message: 'Category is required' }),
    digitizationType: z.nativeEnum(DigitizationType, {
        message: 'Digitization type is required',
    }),
    mediumType: z.nativeEnum(MediumType, {
        message: 'Medium type is required',
    }),
    userIdsWithAccess: z
        .array(z.string())
        .min(1, { message: 'At least one user must have access' }),
})

// Default form values for file creation
export const defaultFileFormValues: PartialFileForm = {
    fileTitle: '',
    confidentiality: Confidentiality.NonConfidential,
    categoryId: 1,
    digitizationType: DigitizationType.Daily,
    mediumType: MediumType.PaperBased,
    userIdsWithAccess: [],
}

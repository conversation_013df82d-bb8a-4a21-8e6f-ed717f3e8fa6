import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { CreateUser } from '@/@types/auth'
import { createNewUserApi } from '@/services/UsersService'

export const useCreateUser = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, CreateUser>({
        mutationFn: createNewUserApi,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['users'] })
            console.log('User created successfully')
        },
        onError: (error) => {
            console.log('Error creating user:', error)
        },
    })
}

import React, { useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import { Tooltip } from '@/components/ui'
import { Tb<PERSON><PERSON>cil, TbTrash } from 'react-icons/tb'
import { useDeleteKeyword } from '@/hooks/keywords'
import useTranslation from '@/utils/hooks/useTranslation'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import type { ColumnDef } from '@tanstack/react-table'
import type { Keyword } from '@/services/Keywords'

interface KeywordsTableProps {
    visibleColumns?: string[]
    onEdit: (keywordId: number) => void
    selectedKeywords: number[]
    onSelectionChange: (selectedIds: number[]) => void
    keywords: Keyword[]
    isLoading: boolean
}

const KeywordsTable = ({
    onEdit,
    // selectedKeywords,
    // onSelectionChange,
    keywords,
    isLoading,
}: KeywordsTableProps) => {
    const { t } = useTranslation()
    const deleteKeywordMutation = useDeleteKeyword()
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
    const [keywordToDelete, setKeywordToDelete] = useState<number | null>(null)

    const handleDeleteClick = (keywordId: number) => {
        setKeywordToDelete(keywordId)
        setDeleteConfirmOpen(true)
    }

    const handleDeleteConfirm = async () => {
        if (keywordToDelete) {
            await deleteKeywordMutation.mutateAsync(keywordToDelete)
            setDeleteConfirmOpen(false)
            setKeywordToDelete(null)
        }
    }

    // const handleSelectionChange = (selectedRowIds: string[]) => {
    //     const selectedIds = selectedRowIds.map(id => parseInt(id, 10))
    //     onSelectionChange(selectedIds)
    // }

    const columns = useMemo<ColumnDef<Keyword>[]>(() => {
        const allColumns: ColumnDef<Keyword>[] = [
            {
                accessorKey: 'id',
                header: t('nav.shared.id'),
            },
            {
                accessorKey: 'value',
                header: t('nav.keywords.keyword'),
                cell: ({ getValue }) => (
                    <span className="font-medium text-gray-900">
                        {getValue<string>()}
                    </span>
                ),
            },
            {
                accessorKey: 'numberOfTimesUsed',
                header: t('nav.keywords.timesUsed'),
                cell: ({ getValue }) => (
                    <span className="text-gray-600">
                        {getValue<number>() || 0}
                    </span>
                ),
            },
            {
                id: 'actions',
                header: t('nav.shared.actions'),
                cell: ({ row }) => (
                    <div className="flex justify-center items-center gap-2 text-lg">
                        <Tooltip title={t('nav.shared.edit')}>
                            <span
                                className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                                onClick={() => onEdit(row.original.id)}
                            >
                                <TbPencil />
                            </span>
                        </Tooltip>
                        <Tooltip title={t('nav.shared.delete')}>
                            <span
                                className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 transition-colors"
                                onClick={() =>
                                    handleDeleteClick(row.original.id)
                                }
                            >
                                <TbTrash />
                            </span>
                        </Tooltip>
                    </div>
                ),
                enableSorting: false,
            },
        ]

        return allColumns
    }, [t, deleteKeywordMutation.isPending, onEdit])

    // const selectedRowIds = useMemo(() => {
    //     return selectedKeywords.map(id => id.toString())
    // }, [selectedKeywords])

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={keywords}
                noData={!isLoading && keywords.length === 0}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                cellBorder={true}
                // selectedRowIds={selectedRowIds}
                // onSelectionChange={handleSelectionChange}
                // getRowId={(row) => row.id.toString()}
            />

            <ConfirmDialog
                isOpen={deleteConfirmOpen}
                type="danger"
                title={t('nav.keywords.deleteKeyword')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={() => setDeleteConfirmOpen(false)}
                onRequestClose={() => setDeleteConfirmOpen(false)}
                onCancel={() => setDeleteConfirmOpen(false)}
                onConfirm={handleDeleteConfirm}
            >
                <p>{t('nav.keywords.deleteKeywordConfirm')}</p>
            </ConfirmDialog>
        </>
    )
}

export default KeywordsTable

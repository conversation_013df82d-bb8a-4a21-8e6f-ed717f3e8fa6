import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteBox } from '@/services/Boxes'

export const useDeleteBox = () => {
    const queryClient = useQueryClient()

    return useMutation<
        void,
        Error,
        { boxId: string | number }
    >({
        mutationFn: ({ boxId }) => deleteBox(boxId),
        onSuccess: () => {
            // Invalidate boxes queries to refetch updated data
            queryClient.invalidateQueries({ queryKey: ['boxes'] })
        },
        onError: (error) => {
            console.error(
                'Failed to delete box:',
                error?.message || 'Unknown error',
            )
        },
    })
}

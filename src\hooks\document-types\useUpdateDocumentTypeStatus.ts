import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocumentTypeStatus } from '@/services/DocumentTypesService'

export const useUpdateDocumentTypeStatus = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ id, status }: { id: number; status: number }) =>
            updateDocumentTypeStatus(id, status),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
            queryClient.invalidateQueries({ queryKey: ['document-type', id] })
        },
        onError: (error) => {
            console.log('Error updating document type status:', error)
        },
    })
}

import { useQuery } from '@tanstack/react-query'
import { getWarehouseArea } from '@/services/Warehouses'
import type { area } from '@/@types/warehouse'

export const useGetWarehouseArea = (warehouseId: string, areaId: string) => {
  return useQuery<area>({
    queryKey: ['warehouse', warehouseId, 'area', areaId],
    queryFn: () => getWarehouseArea(warehouseId, areaId),
    enabled: !!(warehouseId && areaId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
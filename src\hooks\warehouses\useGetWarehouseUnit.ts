import { useQuery } from '@tanstack/react-query'
import { getWarehouseUnit } from '@/services/Warehouses'
import type { Unit } from '@/@types/warehouse'

export const useGetWarehouseUnit = (warehouseId: string, areaId: string, unitId: string) => {
  return useQuery<Unit>({
    queryKey: ['warehouse', warehouseId, 'area', areaId, 'unit', unitId],
    queryFn: () => getWarehouseUnit(warehouseId, areaId, unitId),
    enabled: !!(warehouseId && areaId && unitId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createStock } from '@/services/StockService'
import type { Stock, StockRequest } from '@/@types/stocks'

export const useCreateStock = () => {
    const queryClient = useQueryClient()

    return useMutation<Stock, Error, StockRequest>({
        mutationFn: createStock,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
        },
        onError: (error) => {
            console.log('Error creating stock:', error)
        },
    })
}

import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { Box } from '@/@types/box'
import { useGetBoxesByOrganization } from '@/hooks/boxes'
import Actions from './Actions'
import { formatDateDDMMYY } from '@/utils/dateUtils'

const BoxesListTable = ({
    organizationalNodeId,
}: {
    organizationalNodeId: string
}) => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()

    const pageNumber = parseInt(searchParams.get('pageNumber') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')
    const status = searchParams.get('status')
        ? parseInt(searchParams.get('status')!)
        : undefined

    console.log('BoxesListTable', {
        organizationalNodeId,
        pageNumber,
        pageSize,
        status,
    })

    const { Boxes, isLoading, pagination } = useGetBoxesByOrganization({
        organizationalNodeId,
        pageNumber,
        pageSize,
        status,
    })

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const getBoxStatusLabel = useCallback(
        (status: number) => {
            switch (status) {
                case 0:
                    return {
                        label: t('nav.boxes.open'),
                        className:
                            'bg-green-100 text-green-800 border-green-200',
                    }
                case 1:
                    return {
                        label: t('nav.boxes.closed'),
                        className: 'bg-red-100 text-red-800 border-red-200',
                    }
            }
        },
        [t],
    )

    const tableColumns: ColumnDef<Box>[] = useMemo(() => {
        const cols: ColumnDef<Box>[] = [
            {
                header: t('nav.boxes.boxId'),
                accessorKey: 'boxId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.boxId}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.organization'),
                accessorKey: 'organizationalNodeName',
                cell: ({ row }) => (
                    <div className="font-medium">
                        {row.original.organizationalNodeName}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.filesCount'),
                accessorKey: 'numberOfFiles',
                cell: ({ row }) => (
                    <div className="text-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                            {row.original.numberOfFiles}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.boxes.closureStrap'),
                accessorKey: 'closureStrapSerialNumber',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.closureStrapSerialNumber || 'N/A'}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.status'),
                accessorKey: 'boxStatus',
                cell: ({ row }) => {
                    const status = getBoxStatusLabel(row.original.boxStatus)
                    return (
                        <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${status?.className}`}
                        >
                            {status?.label}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.boxes.createdBy'),
                accessorKey: 'createdByUsername',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.createdByUsername}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.createdAt'),
                accessorKey: 'createdAt',
                cell: ({ row }) => (
                    <div className="text-sm text-gray-600">
                        {formatDateDDMMYY(row.original.createdAt)}
                    </div>
                ),
            },
            {
                header: t('nav.shared.actions'),
                accessorKey: 'action',
                cell: ({ row }) => <Actions boxData={row.original} />,
                enableSorting: false,
            },
        ]

        return cols
    }, [getBoxStatusLabel, t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Boxes || []}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
            />
        </>
    )
}

export default BoxesListTable

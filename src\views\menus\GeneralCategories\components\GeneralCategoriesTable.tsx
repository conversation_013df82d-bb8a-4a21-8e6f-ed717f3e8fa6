import React, { useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import { Tooltip } from '@/components/ui'
import { Tb<PERSON><PERSON>cil, TbTrash } from 'react-icons/tb'
import { useDeleteGeneralCategory } from '@/hooks/genralCategories'
import useTranslation from '@/utils/hooks/useTranslation'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import type { ColumnDef } from '@tanstack/react-table'
import type { GeneralCategory } from '@/services/GeneralCategoriesService'

interface GeneralCategoriesTableProps {
    visibleColumns?: string[]
    onEdit: (generalCategoryId: number) => void
    selectedGeneralCategories: number[]
    onSelectionChange: (selectedIds: number[]) => void
    generalCategories: GeneralCategory[]
    isLoading: boolean
}

const GeneralCategoriesTable = ({
    onEdit,
    selectedGeneralCategories,
    onSelectionChange,
    generalCategories,
    isLoading,
}: GeneralCategoriesTableProps) => {
    const { t } = useTranslation()
    const deleteGeneralCategoryMutation = useDeleteGeneralCategory()
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
    const [generalCategoryToDelete, setGeneralCategoryToDelete] = useState<number | null>(null)

    const handleDeleteClick = (generalCategoryId: number) => {
        setGeneralCategoryToDelete(generalCategoryId)
        setDeleteConfirmOpen(true)
    }

    const handleDeleteConfirm = async () => {
        if (generalCategoryToDelete) {
            await deleteGeneralCategoryMutation.mutateAsync(generalCategoryToDelete)
            setDeleteConfirmOpen(false)
            setGeneralCategoryToDelete(null)
        }
    }

    const columns = useMemo<ColumnDef<GeneralCategory>[]>(() => {
        return [
            {
                header: t('nav.shared.id'),
                accessorKey: 'id',
                cell: ({ getValue }) => (
                    <div className="flex items-center">
                        <span className="ml-2 rtl:mr-2 rtl:ml-0">
                            {getValue<number>()}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.generalCategories.name'),
                accessorKey: 'name',
                cell: ({ getValue }) => (
                    <div className="flex items-center">
                        <span className="ml-2 rtl:mr-2 rtl:ml-0 font-semibold">
                            {getValue<string>()}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.shared.actions'),
                accessorKey: 'actions',
                cell: ({ row }) => (
                    <div className="flex justify-end text-lg">
                        <Tooltip title={t('nav.shared.edit')}>
                            <span
                                className="cursor-pointer p-2 hover:text-indigo-600"
                                onClick={() => onEdit(row.original.id)}
                            >
                                <TbPencil />
                            </span>
                        </Tooltip>
                        <Tooltip title={t('nav.shared.delete')}>
                            <span
                                className="cursor-pointer p-2 hover:text-red-500"
                                onClick={() => handleDeleteClick(row.original.id)}
                            >
                                <TbTrash />
                            </span>
                        </Tooltip>
                    </div>
                ),
            },
        ]
    }, [t, onEdit])

    const selectedRowIds = useMemo(() => {
        return selectedGeneralCategories.reduce((acc, id) => {
            acc[id.toString()] = true
            return acc
        }, {} as Record<string, boolean>)
    }, [selectedGeneralCategories])

    const handleSelectionChange = (selectedRowIds: Record<string, boolean>) => {
        const selectedIds = Object.keys(selectedRowIds)
            .filter(id => selectedRowIds[id])
            .map(id => parseInt(id))
        onSelectionChange(selectedIds)
    }

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={generalCategories}
                noData={!isLoading && generalCategories.length === 0}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                cellBorder={true}
                selectedRowIds={selectedRowIds}
                onSelectionChange={handleSelectionChange}
                getRowId={(row) => row.id.toString()}
            />

            <ConfirmDialog
                isOpen={deleteConfirmOpen}
                type="danger"
                title={t('nav.generalCategories.deleteGeneralCategory')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={() => setDeleteConfirmOpen(false)}
                onRequestClose={() => setDeleteConfirmOpen(false)}
                onCancel={() => setDeleteConfirmOpen(false)}
                onConfirm={handleDeleteConfirm}
            >
                <p>{t('nav.generalCategories.deleteGeneralCategoryConfirm')}</p>
            </ConfirmDialog>
        </>
    )
}

export default GeneralCategoriesTable

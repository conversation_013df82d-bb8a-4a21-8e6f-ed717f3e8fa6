import { useMutation, useQueryClient } from '@tanstack/react-query'
import { markDocumentAsDigitizedApi } from '@/services/DocumentsService'

export const useMarkDocumentAsDigitized = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ documentId }: { documentId: string }) =>
            markDocumentAsDigitizedApi(documentId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
        },
        onError: (error) => {
            console.log(error?.message || 'Failed to mark document as digitized')
        },
    })
}
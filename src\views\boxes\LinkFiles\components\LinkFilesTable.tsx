import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { File } from '@/@types/file'
import { useGetFilesByStation } from '@/hooks/files'
import { useGetMyDigitizationStations } from '@/hooks/digitization-station'

interface LinkFilesTableProps {
    selectedFileIds: (number | string)[]
    onSelectionChange: (ids: (number | string)[]) => void
}

const LinkFilesTable = ({ selectedFileIds, onSelectionChange }: LinkFilesTableProps) => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()

    console.log('selections', selectedFileIds)

    const { data: stations = [] } = useGetMyDigitizationStations()
    const { Files, isLoading, pagination } = useGetFilesByStation({
        stationId: stations?.[0]?.id || 0,
        status: 1,
    })

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const tableColumns: ColumnDef<File>[] = useMemo(() => {
        const cols: ColumnDef<File>[] = [
            {
                header: 'ID',
                accessorKey: 'fileId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.fileId}
                    </div>
                ),
            },
            {
                header: t('nav.files.title'),
                accessorKey: 'title',
                cell: ({ row }) => (
                    <div className="font-medium">{row.original.fileTitle}</div>
                ),
            },
            {
                header: t('nav.files.category'),
                accessorKey: 'categoryName',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.categoryName || 'N/A'}
                    </div>
                ),
            },
            {
                header: t('nav.files.confidentiality'),
                accessorKey: 'confidentiality',
                cell: ({ row }) => (
                    <div className="text-sm">{row.original.mediumType}</div>
                ),
            },
            {
                header: 'pages',
                accessorKey: 'numberOfPages',
                cell: ({ row }) => (
                    <div className="text-sm">{row.original.numberOfPages}</div>
                ),
            },
        ]

        return cols
    }, [t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Files || []}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                checkboxChecked={(row) => selectedFileIds.includes(row.fileId as never)}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
                // for checked rows

                onCheckBoxChange={(checked: boolean, row) => {
                    onSelectionChange(
                        checked 
                            ? [...selectedFileIds, row.fileId] 
                            : selectedFileIds.filter(id => id !== row.fileId)
                    )
                }}
                onIndeterminateCheckBoxChange={(checked: boolean, rows) => {
                    onSelectionChange(
                        checked ? rows.map((r) => r.original.fileId) : []
                    )
                }}
            />
        </>
    )
}

export default LinkFilesTable

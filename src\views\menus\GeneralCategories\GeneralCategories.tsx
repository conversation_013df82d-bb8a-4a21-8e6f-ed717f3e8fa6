import { useState } from 'react'
import { AdaptiveCard, Container } from '@/components/shared'
import {
    useGetGeneralCategories,
    useDeleteMultipleGeneralCategories,
    useGeneralCategorySearch,
} from '@/hooks/genralCategories'
import GeneralCategoryHeader from './components/GeneralCategoryHeader'
import GeneralCategoryActionTools from './components/GeneralCategoryActionTools'
import GeneralCategoriesTable from './components/GeneralCategoriesTable'
import GeneralCategoryDialog from './components/GeneralCategoryDialog'
import GeneralCategorySelectionFooter from './components/GeneralCategorySelectionFooter'
import { TbCategory } from 'react-icons/tb'
import CountBadges from '@/components/shared/displaying/CountBadges'
import useTranslation from '@/utils/hooks/useTranslation'

const GeneralCategories = () => {
    const { t } = useTranslation()
    const {
        data: generalCategories = [],
        isLoading,
        isError,
    } = useGetGeneralCategories()
    const {
        results: searchResults,
        isLoading: isSearchLoading,
        search,
    } = useGeneralCategorySearch()

    const deleteMultipleGeneralCategoriesMutation =
        useDeleteMultipleGeneralCategories()
    const [dialogOpen, setDialogOpen] = useState(false)
    const [editingGeneralCategoryId, setEditingGeneralCategoryId] = useState<
        number | null
    >(null)
    const [visibleColumns, setVisibleColumns] = useState([
        'id',
        'name',
        'actions',
    ])
    const [selectedGeneralCategories, setSelectedGeneralCategories] = useState<
        number[]
    >([])
    const [searchTerm, setSearchTerm] = useState('')

    const handleAddGeneralCategory = () => {
        setEditingGeneralCategoryId(null)
        setDialogOpen(true)
    }

    const handleEditGeneralCategory = (generalCategoryId: number) => {
        setEditingGeneralCategoryId(generalCategoryId)
        setDialogOpen(true)
    }

    const handleCloseDialog = () => {
        setDialogOpen(false)
        setEditingGeneralCategoryId(null)
    }

    const handleSearch = (term: string) => {
        setSearchTerm(term)
        search(term)
    }

    const handleDeleteSelected = async () => {
        if (selectedGeneralCategories.length > 0) {
            await deleteMultipleGeneralCategoriesMutation.mutateAsync(
                selectedGeneralCategories,
            )
            setSelectedGeneralCategories([])
        }
    }

    const handleClearSelection = () => {
        setSelectedGeneralCategories([])
    }

    // Determine which data to display
    const displayData = searchTerm ? searchResults : generalCategories
    const displayLoading = isLoading || isSearchLoading

    return (
        <>
            <Container>
                <AdaptiveCard>
                    {/* Header with Count Badges */}
                    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                        <div className="flex items-center gap-4">
                            <CountBadges
                                // icon={<TbCategory className="text-xl" />}
                                // label={t(
                                //     'nav.generalCategories.generalCategories',
                                // )}
                                counts={{ total: generalCategories.length }}
                                loading={displayLoading}
                                error={isError}
                            />
                        </div>
                        <GeneralCategoryHeader
                            onAddGeneralCategory={handleAddGeneralCategory}
                        />
                    </div>

                    {/* Action Tools */}
                    <div className="">
                        <GeneralCategoryActionTools
                            onColumnVisibilityChange={setVisibleColumns}
                            onSearch={handleSearch}
                        />
                    </div>

                    {/* Table */}
                    <div className="">
                        <GeneralCategoriesTable
                            visibleColumns={visibleColumns}
                            selectedGeneralCategories={
                                selectedGeneralCategories
                            }
                            generalCategories={displayData}
                            isLoading={displayLoading}
                            onEdit={handleEditGeneralCategory}
                            onSelectionChange={setSelectedGeneralCategories}
                        />
                    </div>

                    {/* Dialog */}
                    <GeneralCategoryDialog
                        isOpen={dialogOpen}
                        generalCategoryId={editingGeneralCategoryId}
                        onClose={handleCloseDialog}
                    />
                </AdaptiveCard>
            </Container>

            {/* Selection Footer */}
            <GeneralCategorySelectionFooter
                selectedCount={selectedGeneralCategories.length}
                onDeleteSelected={handleDeleteSelected}
                onClearSelection={handleClearSelection}
            />
        </>
    )
}

export default GeneralCategories

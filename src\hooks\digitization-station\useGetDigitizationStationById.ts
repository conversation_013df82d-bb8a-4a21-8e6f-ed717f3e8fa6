import { useQuery } from '@tanstack/react-query'
import { getDigitizationStationById } from '@/services/DigitizationStationService'
import type { DigitizationStationSpecifications } from '@/@types/digitizationStation'

export const useGetDigitizationStationById = (id: string | number) => {
  return useQuery<DigitizationStationSpecifications>({
    queryKey: ['digitization-station', id],
    queryFn: () => getDigitizationStationById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
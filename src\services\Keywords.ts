import ApiService from './ApiService'

const BASE_ROUTE = '/Keywords'

export interface Keyword {
    id: number
    value: string
    numberOfTimesUsed: number
}

export async function getKeywordsApi() {
    const data = ApiService.get<Keyword[]>(BASE_ROUTE)
    console.log('data ', data)
    return data
}

export async function create<PERSON><PERSON><PERSON><PERSON><PERSON>(value: string) {
    return ApiService.post<Keyword>(BASE_ROUTE, { value })
}

export async function updateKeyword<PERSON><PERSON>(id: number, value: string) {
    return ApiService.put<Keyword>(`${BASE_ROUTE}/${id}`, { value })
}

export async function deleteKeyword<PERSON>pi(id: number) {
    return ApiService.delete(`${BASE_ROUTE}/${id}`)
}

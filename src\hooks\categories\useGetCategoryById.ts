import { useQuery } from '@tanstack/react-query'
import { getCategoryById } from '@/services/CategoriesService'
import type { CategoryDetail } from '@/@types/categories'

interface GetCategoryByIdParams {
    id: number
    includeDeleted?: boolean
    includeRelated?: boolean
}

export const useGetCategoryById = ({
    id,
    includeDeleted = false,
    includeRelated = true,
}: GetCategoryByIdParams) => {
    return useQuery<CategoryDetail>({
        queryKey: ['category', id, { includeDeleted, includeRelated }],
        queryFn: () => getCategoryById(id, { includeDeleted, includeRelated }),
        enabled: !!id,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

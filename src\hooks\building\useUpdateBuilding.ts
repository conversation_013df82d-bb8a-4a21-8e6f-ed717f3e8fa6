import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateBuilding } from '@/services/BuildingService'
import { BuildingForm } from '@/@types/building'

export const useUpdateBuilding = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, building }: { id: number; building: BuildingForm }) => 
      updateBuilding(id, building),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] })
      queryClient.invalidateQueries({ queryKey: ['building', id] })
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to update building')
    },
  })
}
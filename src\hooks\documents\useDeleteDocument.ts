import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteDocument } from '@/services/DocumentsService'

export const useDeleteDocument = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (documentId: string) => deleteDocument(documentId),
    onSuccess: (_, documentId) => {
      queryClient.invalidateQueries({ queryKey: ['document', documentId] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
    },
    onError: (error) => {
      console.log(error?.message || 'Failed to delete document')
    },
  })
}

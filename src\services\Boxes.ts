import ApiService from './ApiService'
import { BoxDetails, BoxesResponse } from '@/@types/box'

// this for admin only
export async function getBoxes({
    organizationalNodeId,
    pageNumber = 1,
    pageSize = 10,
    boxStatus = undefined,
}: {
    organizationalNodeId?: string | number
    pageNumber?: number
    pageSize?: number
    boxStatus?: number | undefined
}) {
    return ApiService.get<BoxesResponse>(`/Boxes`, {
        params: { organizationalNodeId, pageNumber, pageSize, boxStatus },
    })
}

export async function getBoxesByOrganization(
    organizationalNodeId: string,
    {
        pageNumber = 1,
        pageSize = 10,
        boxStatus = undefined,
    }: {
        pageNumber?: number
        pageSize?: number
        boxStatus?: number | undefined
    },
) {
    return ApiService.get<BoxesResponse>(
        `/Boxes/organization/${organizationalNodeId}`,
        {
            params: { pageNumber, pageSize, boxStatus },
        },
    )
}

// GET
// /api/Boxes/{boxId}
// Gets a specific box by its ID
export async function getBoxById(boxId: string | number) {
    return ApiService.get<BoxDetails>(`/Boxes/${boxId}`)
}

// POST
// /api/Boxes
// Creates a new box for an organization
export async function createBox(organizationalNodeId: number | string) {
    return ApiService.post<BoxDetails>(`/Boxes`, { organizationalNodeId })
}

// POST
// /api/Boxes/{boxId}/close
// Closes a box and assigns a closure strap
export async function closeBox(boxId: string | number) {
    return ApiService.post<void>(`/Boxes/${boxId}/close`)
}

// DELETE
// /api/Boxes/{boxId}
// Deletes a box and unlinks all files
export async function deleteBox(boxId: string | number) {
    return ApiService.delete<void>(`/Boxes/${boxId}`)
}

// POST
// /api/Boxes/{boxId}/link-files
// Links multiple files to a box
export async function linkFilesToBox(
    boxId: string | number,
    fileIds: string[],
) {
    return ApiService.post<void>(`/Boxes/${boxId}/link-files`, { fileIds })
}

// POST
// /api/Boxes/{boxId}Unlink-files
// Unlinks multiple files from a box
export async function unlinkFilesFromBox(
    boxId: string | number,
    fileIds: string[],
) {
    return ApiService.post<void>(`/Boxes/${boxId}/unlink-files`, { fileIds })
}

import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import AllBoxesActionTools from './components/AllBoxesActionTools'
import AllBoxesTableTools from './components/AllBoxesTableTools'
import AllBoxesTable from './components/AllBoxesTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbBox } from 'react-icons/tb'

const AllBoxes = () => {
    const { t } = useTranslation()

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbBox className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="">{t('nav.boxes.allBoxes')}</h3>
                            </div>
                            <AllBoxesActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <AllBoxesTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <AllBoxesTable />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>
        </>
    )
}

export default AllBoxes

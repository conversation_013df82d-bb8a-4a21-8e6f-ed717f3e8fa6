/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState, useCallback } from 'react'
import * as signalR from '@microsoft/signalr'

type KeywordResult = {
    id: string
    value: string
    score: number
}

type UseKeywordSearchReturn = {
    isConnected: boolean
    isLoading: boolean
    error: string | null
    results: KeywordResult[]
    search: (term: string) => void
}

export function useKeywordSearch(): UseKeywordSearchReturn {
    const hubUrl = 'https://archiving-system.runasp.net/hubs/keyword-search'
    const [isConnected, setIsConnected] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [results, setResults] = useState<KeywordResult[]>([])

    const connectionRef = useRef<signalR.HubConnection | null>(null)
    const searchTimeout = useRef<NodeJS.Timeout | null>(null)

    // init connection
    useEffect(() => {
        const connection = new signalR.HubConnectionBuilder()
            .withUrl(hubUrl, {
                transport: signalR.HttpTransportType.WebSockets,
                skipNegotiation: true,
            })
            .build()

        connection.onclose(() => setIsConnected(false))
        connection.onreconnecting(() => setIsConnected(false))
        connection.onreconnected(() => setIsConnected(true))

        connection.on('SearchResults', (res: any) => {
            setResults(res.results || [])
            setIsLoading(false)
        })

        connection.on('SearchError', (err: any) => {
            setError(err.error)
            setIsLoading(false)
        })

        connection
            .start()
            .then(() => setIsConnected(true))
            .catch((err) => setError(err.message))

        connectionRef.current = connection

        return () => {
            connection.stop()
        }
    }, [hubUrl])

    const search = useCallback(
        (term: string) => {
            if (!connectionRef.current || !isConnected) {
                setError('Not connected to hub')
                return
            }

            if (searchTimeout.current) clearTimeout(searchTimeout.current)

            searchTimeout.current = setTimeout(() => {
                if (!term.trim()) {
                    setResults([])
                    return
                }

                setIsLoading(true)
                setError(null)

                connectionRef
                    .current!.invoke('SearchKeywords', term, 10, 60)
                    .catch((err) => {
                        setError(err.message)
                        setIsLoading(false)
                    })
            }, 300) // debounce 300ms
        },
        [isConnected],
    )

    return { isConnected, isLoading, error, results, search }
}

import Select from '@/components/ui/Select'
import { useGetBoxesByOrganization } from '@/hooks/boxes'
import useTranslation from '@/utils/hooks/useTranslation'
import type { Box } from '@/@types/box'

interface BoxDropdownProps {
    value: string
    onChange: (value: string) => void
    placeholder?: string
    organizationalNodeId?: string
}

const BoxDropdown = ({
    value,
    onChange,
    placeholder,
    organizationalNodeId,
}: BoxDropdownProps) => {
    const { t } = useTranslation()
    const storage = window.localStorage
    const orgId = organizationalNodeId || storage.getItem('org_code')!

    // Fetch boxes with status 1 and above
    const { data: boxesData, isLoading } = useGetBoxesByOrganization({
        organizationalNodeId: orgId,
        pageNumber: 1,
        pageSize: 20,
        status: 0,
    })

    const options = boxesData?.items.map((box: Box) => ({
        value: box.boxId,
        label: `${box.boxId}`,
    }))

    const selectedOption = options?.find((option) => option.value === value)

    return (
        <Select
            options={options}
            value={selectedOption}
            placeholder={placeholder || t('nav.boxes.selectBox')}
            isLoading={isLoading}
            className="min-w-[250px]"
            onChange={(option) => onChange(option?.value || '')}
        />
    )
}

export default BoxDropdown

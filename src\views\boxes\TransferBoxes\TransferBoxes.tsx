import { useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import TransferBoxesActionTools from './components/TransferBoxesActionTools'
import TransferBoxesTableTools from './components/TransferBoxesTableTools'
import TransferBoxesTable from './components/TransferBoxesTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbTruck } from 'react-icons/tb'
import { useTransferBoxesToWarehouse } from '@/hooks/warehouses'

const TransferBoxes = () => {
    const { t } = useTranslation()
    const [selectedBoxIds, setSelectedBoxIds] = useState<(number | string)[]>(
        [],
    )

    const { mutate: transferBoxes, isSuccess } = useTransferBoxesToWarehouse()

    const handleTransferBoxes = (
        warehouseId: string,
        boxIds: string[],
        notes: string,
    ) => {
        transferBoxes({ warehouseId, boxes: boxIds, notes })
        if (isSuccess) {
            setSelectedBoxIds([])
        }
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbTruck className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="">
                                    {t('nav.transferBoxes.transferBoxes')}
                                </h3>
                            </div>
                            <TransferBoxesActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <TransferBoxesTableTools
                                selectedCount={selectedBoxIds.length}
                                selectedBoxIds={selectedBoxIds.map((id) =>
                                    String(id),
                                )}
                                onTransferBoxes={handleTransferBoxes}
                            />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <TransferBoxesTable
                                selectedBoxIds={selectedBoxIds}
                                onSelectionChange={setSelectedBoxIds}
                            />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>
        </>
    )
}

export default TransferBoxes

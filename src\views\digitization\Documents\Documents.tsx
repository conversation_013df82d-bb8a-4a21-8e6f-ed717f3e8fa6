import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import DocumentsListActionTools from './components/DocumentsListActionTools'
import DocumentsListTableTools from './components/DocumentsListTableTools'
import DocumentsListTable from './components/DocumentsListTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbFileText } from 'react-icons/tb'
import { useParams } from 'react-router-dom'

const Documents = () => {
    const { t } = useTranslation()
    const { fileId } = useParams()

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbFileText className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="">
                                    {t('nav.documents.documents')}
                                </h3>
                            </div>
                            <DocumentsListActionTools fileId={fileId!} />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <DocumentsListTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <DocumentsListTable fileId={fileId!} />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>
        </>
    )
}

export default Documents

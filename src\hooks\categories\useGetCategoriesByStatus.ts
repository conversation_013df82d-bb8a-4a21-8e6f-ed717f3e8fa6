import { useQuery } from '@tanstack/react-query'
import { getCategoriesByStatus } from '@/services/CategoriesService'
import type { Category } from '@/@types/categories'
import { Status } from '@/@types/common'

interface CategoriesStatusParams {
    status: Status
    includeDeleted?: boolean
    parentId?: number
    rootCategoriesOnly?: boolean
}

export const useGetCategoriesByStatus = ({
    status,
    includeDeleted = false,
    parentId,
    rootCategoriesOnly = false,
}: CategoriesStatusParams) => {
    return useQuery<Category[]>({
        queryKey: [
            'categories',
            'status',
            status,
            { includeDeleted, parentId, rootCategoriesOnly },
        ],
        queryFn: () =>
            getCategoriesByStatus({
                status,
                includeDeleted,
                parentId,
                rootCategoriesOnly,
            }),
        enabled: status !== undefined && status !== null,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

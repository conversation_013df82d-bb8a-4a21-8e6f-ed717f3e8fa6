/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { addUnitsToArea } from '@/services/Warehouses'
import type { WarehouseUnit } from '@/@types/warehouse'

export const useAddUnitsToArea = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        { warehouseId: string; areaId: string; units: WarehouseUnit }
    >({
        mutationFn: ({ warehouseId, areaId, units }) =>
            addUnitsToArea(warehouseId, areaId, units),
        onSuccess: (_, { warehouseId, areaId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId, 'area', areaId],
            })
        },
        onError: (error) => {
            console.log('Error adding units to area:', error)
        },
    })
}

import { useQuery } from '@tanstack/react-query'
import { getWarehouseById } from '@/services/Warehouses'
import type { WarehouseDetails } from '@/@types/warehouse'

export const useGetWarehouseById = (id: string) => {
  return useQuery<WarehouseDetails>({
    queryKey: ['warehouse', id],
    queryFn: () => getWarehouseById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
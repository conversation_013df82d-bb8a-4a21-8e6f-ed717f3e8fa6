import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createDocumentType } from '@/services/DocumentTypesService'

export const useCreateDocumentType = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: createDocumentType,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['document-types'] })
        },
        onError: (error) => {
            console.log('Error creating document type:', error)
        },
    })
}

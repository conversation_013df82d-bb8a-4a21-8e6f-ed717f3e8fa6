import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateStock } from '@/services/StockService'
import type { Stock, StockRequest } from '@/@types/stocks'

export const useUpdateStock = () => {
    const queryClient = useQueryClient()

    return useMutation<Stock, Error, { id: number; stock: StockRequest }>({
        mutationFn: ({ id, stock }) => updateStock(id, stock),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
            queryClient.invalidateQueries({ queryKey: ['stock', id] })
        },
        onError: (error) => {
            console.log('Error updating stock:', error)
        },
    })
}

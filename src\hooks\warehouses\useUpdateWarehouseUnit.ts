/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouseUnit } from '@/services/Warehouses'

export const useUpdateWarehouseUnit = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        {
            warehouseId: string
            areaId: string
            unitId: string
            unit: { length: number; width: number }
        }
    >({
        mutationFn: ({ warehouseId, areaId, unitId, unit }) =>
            updateWarehouseUnit(warehouseId, areaId, unitId, unit),
        onSuccess: (_, { warehouseId, areaId, unitId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: [
                    'warehouse',
                    warehouseId,
                    'area',
                    areaId,
                    'unit',
                    unitId,
                ],
            })
        },
        onError: (error) => {
            console.log('Error updating warehouse unit:', error)
        },
    })
}

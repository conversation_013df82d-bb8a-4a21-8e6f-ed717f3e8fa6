import { useQuery } from '@tanstack/react-query'
import { getConsumablesByStock } from '@/services/ConsumableService'

export const useGetConsumablesByStock = (stockId: number) => {
    return useQuery({
        queryKey: ['consumables', 'by-stock', stockId],
        queryFn: () => getConsumablesByStock(stockId),
        enabled: !!stockId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}
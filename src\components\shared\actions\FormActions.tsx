/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'

interface FormActionsProps {
    isEdit?: boolean
    data?: any
    loading?: boolean
    onCancel?: () => void
    onSubmit?: () => void
    onStatusChange?: () => void
    showStatusButton?: boolean
    statusButtonText?: string
    statusButtonColor?: 'red' | 'green' | 'blue' | 'emerald'
    submitButtonText?: string
    cancelButtonText?: string
    disabled?: boolean
    className?: string
}

const FormActions: React.FC<FormActionsProps> = ({
    isEdit = false,
    data,
    loading = false,
    onSubmit,
    onStatusChange,
    showStatusButton = false,
    statusButtonText,
    statusButtonColor = 'blue',
    submitButtonText,
    disabled = false,
    className = '',
}) => {
    const { t } = useTranslation()

    const getStatusButtonClasses = () => {
        const colorClasses = {
            red: 'bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600',
            green: 'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600',
            blue: 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600',
            emerald:
                'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600',
        }
        return colorClasses[statusButtonColor]
    }

    return (
        <div className={`flex items-center justify-end gap-3 ${className}`}>
            {showStatusButton && isEdit && data && onStatusChange && (
                <Button
                    size="sm"
                    type="button"
                    disabled={loading || disabled}
                    variant="solid"
                    className={getStatusButtonClasses()}
                    onClick={onStatusChange}
                >
                    {statusButtonText ||
                        (data?.status === 1
                            ? t('nav.GlobalActions.frozen')
                            : t('nav.GlobalActions.approve'))}
                </Button>
            )}

            <Button
                size="sm"
                type="submit"
                variant="solid"
                loading={loading}
                disabled={disabled}
                className="min-w-[100px]"
                onClick={onSubmit}
            >
                {submitButtonText ||
                    (isEdit ? t('nav.shared.update') : t('nav.shared.create'))}
            </Button>
        </div>
    )
}

export default FormActions

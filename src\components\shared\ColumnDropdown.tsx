import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Dropdown } from '@/components/ui'
import { useColumnStore, Column } from '@/store/columnStore'

interface ColumnDropdownProps {
    tableId: string
    defaultColumns: Column[]
    className?: string
}

const ColumnDropdown: React.FC<ColumnDropdownProps> = ({
    tableId,
    defaultColumns,
}) => {
    const { t } = useTranslation()

    const { getColumns, setColumns, toggleColumn } = useColumnStore()

    // Initialize columns if not exists
    useEffect(() => {
        const existingColumns = getColumns(tableId)
        if (existingColumns.length === 0) {
            setColumns(tableId, defaultColumns)
        }
    }, [tableId, defaultColumns, getColumns, setColumns])

    const columns = getColumns(tableId)

    const handleToggleColumn = (columnKey: string) => {
        toggleColumn(tableId, columnKey)
    }

    return (
        <Dropdown title={t('columns.customize', 'Columns')}>
            {columns.map((column) => (
                <label
                    key={column.key}
                    className="flex items-center gap-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer"
                >
                    <input
                        type="checkbox"
                        checked={column.visible}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        onChange={() => handleToggleColumn(column.key)}
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                        {column.label}
                    </span>
                </label>
            ))}
        </Dropdown>
    )
}

export default ColumnDropdown

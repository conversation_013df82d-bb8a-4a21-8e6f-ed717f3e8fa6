import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbFolderX } from 'react-icons/tb'

interface BoxesListTableToolsProps {
    selectedCount?: number
    onCloseBoxes?: (boxIds: string[]) => void
}

const BoxesListTableTools = ({
    selectedCount = 0,
    onCloseBoxes,
}: BoxesListTableToolsProps) => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // Implement search logic here
        console.log('Search:', value)
    }

    return (
        <div className="space-y-4 flex items-center justify-between gap-2">
            {/* Search Row */}
            <div className="flex-1">
                <Input
                    placeholder={t('nav.shared.search')}
                    value={searchTerm}
                    prefix={<TbSearch className="text-lg" />}
                    className="w-full"
                    onChange={(e) => handleSearch(e.target.value)}
                />
            </div>

            {/* Action Buttons Row */}
            <div className="flex flex-wrap items-center gap-3">
                {selectedCount > 0 && (
                    <>
                        <Button
                            variant="default"
                            size="sm"
                            icon={<TbFolderX />}
                            className="flex items-center gap-2"
                            onClick={() => onCloseBoxes?.([])}
                        >
                            {t('nav.boxes.closeBoxes')}
                        </Button>
                        <span className="text-sm text-gray-600 ml-2">
                            {selectedCount} {t('nav.shared.selected')}
                        </span>
                    </>
                )}
            </div>
        </div>
    )
}

export default BoxesListTableTools

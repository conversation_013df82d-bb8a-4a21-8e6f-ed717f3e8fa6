import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateStockStatus } from '@/services/StockService'
import type { Status } from '@/@types/common'

export const useUpdateStockStatus = () => {
    const queryClient = useQueryClient()

    return useMutation<void, Error, { id: number; newStatus: Status }>({
        mutationFn: ({ id, newStatus }) => updateStockStatus(id, newStatus),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['stocks'] })
            queryClient.invalidateQueries({ queryKey: ['stock', id] })
        },
        onError: (error) => {
            console.log('Error updating stock status:', error)
        },
    })
}

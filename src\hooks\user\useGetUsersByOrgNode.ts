import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { getUsersByOrganizationalNodeApi } from '@/services/UsersService'

// Interface for standardized user options format
export interface UserOption {
    value: string
    name: string
}

export const useGetUsersByOrgNode = (organizationalNodeCode: string) => {
    const query = useQuery({
        queryKey: ['usersByOrgNode', organizationalNodeCode],
        queryFn: () => getUsersByOrganizationalNodeApi(organizationalNodeCode),
        enabled: !!organizationalNodeCode,
        staleTime: 1000 * 60 * 5,
        gcTime: 1000 * 60 * 10,
        retry: 2,
    })

    // Memoize users as options to avoid unnecessary recalculations
    const usersAsOptions = useMemo(() => {
        if (!query.data) return []
        return query.data.map((user: { name: string; code: string }) => ({
            value: user.code,
            name: user.name,
        }))
    }, [query.data])

    return {
        ...query,
        usersAsOptions,
        // Keep original data for backward compatibility
        data: query.data,
        // Convenience accessor
        users: query.data || [],
    }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateWarehouseStatus } from '@/services/Warehouses'
import type { Status } from '@/@types/common'

export const useUpdateWarehouseStatus = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        { id: string; statusUpdate: { newStatus: Status } }
    >({
        mutationFn: ({ id, statusUpdate }) =>
            updateWarehouseStatus(id, statusUpdate),
        onSuccess: (_, { id }) => {
            queryClient.invalidateQueries({ queryKey: ['warehouses'] })
            queryClient.invalidateQueries({ queryKey: ['warehouse', id] })
        },
        onError: (error) => {
            console.log('Error updating warehouse status:', error)
        },
    })
}

import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import DocumentsForm from './DocumentsForm'
import useTranslation from '@/utils/hooks/useTranslation'
import type { DocumentCreate } from '@/@types/document'
import { TbPlus } from 'react-icons/tb'
import { useCreateDocument } from '@/hooks/documents'

interface DocumentsModalProps {
    isOpen: boolean
    onSuccess: () => void
    onClose: () => void
    fileId: string
    documentId?: string
}

const DocumentsModal = ({
    isOpen,
    onSuccess,
    onClose,
    fileId,
}: DocumentsModalProps) => {
    const { t } = useTranslation()
    const { mutate: createDocument } = useCreateDocument()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleSubmit = async (formData: DocumentCreate) => {
        const createData = {
            ...formData,
            fileId: fileId,
        }
        setIsSubmitting(true)

        createDocument({ document: createData })
        setIsSubmitting(false)
        onSuccess()
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center gap-2">
                    <TbPlus className="text-lg" />

                    <h5 className="text-lg font-semibold">
                        {t('nav.Document.addDocument')}
                    </h5>
                </div>
            </div>

            <div className="p-4">
                <DocumentsForm
                    isSubmitting={isSubmitting}
                    onSubmit={handleSubmit}
                    onCancel={handleClose}
                />
            </div>
        </Dialog>
    )
}

export default DocumentsModal

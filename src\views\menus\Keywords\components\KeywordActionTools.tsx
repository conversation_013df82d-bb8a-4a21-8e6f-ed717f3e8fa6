import KeywordsSearch from './KeywordsSearch'
import KeywordFilter from './KeywordFilter'

interface KeywordActionToolsProps {
    onColumnVisibilityChange?: (columns: string[]) => void
    onSearch?: (term: string) => void
}

const KeywordActionTools = ({
    onColumnVisibilityChange,
    onSearch,
}: KeywordActionToolsProps) => {
    return (
        <div className="flex gap-4 flex-row justify-center items-center ">
            <KeywordsSearch onInputChange={onSearch} />

            <KeywordFilter
                onColumnVisibilityChange={onColumnVisibilityChange}
            />
        </div>
    )
}

export default KeywordActionTools

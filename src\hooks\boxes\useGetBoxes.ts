import { useQuery } from '@tanstack/react-query'
import { getBoxes } from '@/services/Boxes'
import { BoxesResponse } from '@/@types/box'
import { PaginationResponse } from '@/@types/global'

interface UseGetBoxesParams {
    organizationalNodeId?: string | number
    pageNumber?: number
    pageSize?: number
    boxStatus?: number | undefined
}

export const useGetBoxes = ({
    organizationalNodeId = undefined,
    pageNumber = 1,
    pageSize = 10,
    boxStatus = undefined,
}: UseGetBoxesParams) => {
    const query = useQuery<BoxesResponse>({
        queryKey: [
            'boxes',
            'admin',
            organizationalNodeId,
            pageNumber,
            pageSize,
            boxStatus,
        ],
        queryFn: () =>
            getBoxes({ organizationalNodeId, pageNumber, pageSize, boxStatus }),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })

    return {
        ...query,
        Boxes: query.data?.items,
        pagination: query.data as PaginationResponse,
    }
}

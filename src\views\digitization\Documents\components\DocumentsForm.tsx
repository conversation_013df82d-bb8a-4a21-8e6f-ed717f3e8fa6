import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import useTranslation from '@/utils/hooks/useTranslation'
import type { DocumentCreate } from '@/@types/document'
import { useGetDocumentTypes } from '@/hooks/document-types'
import { useGetGeneralCategories } from '@/hooks/genralCategories'

const documentSchema = z.object({
    title: z.string().min(1, 'Document title is required'),
    documentTypeId: z.number().min(1, 'Document type is required'),
    generalCategoryId: z.number().min(1, 'General category is required'),
    date: z.string().min(1, 'Date is required'),
})

type DocumentFormData = z.infer<typeof documentSchema>

interface DocumentsFormProps {
    isSubmitting: boolean
    onSubmit: (data: DocumentCreate) => void
    onCancel: () => void
}

const DocumentsForm = ({
    isSubmitting,
    onSubmit,
    onCancel,
}: DocumentsFormProps) => {
    const { t } = useTranslation()

    const { data: types, isLoading } = useGetDocumentTypes()
    const { data: generals } = useGetGeneralCategories()

    const documentTypes =
        types?.map((type) => ({
            value: type.id,
            label: type.name,
        })) || []

    const generalCategories =
        generals?.map((general) => ({
            value: general.id.toString(),
            label: general.name,
        })) || []

    const {
        register,
        handleSubmit,
        formState: { errors },
        control,
    } = useForm<DocumentFormData>({
        resolver: zodResolver(documentSchema),
        defaultValues: {
            title: '',
            documentTypeId: 1,
            generalCategoryId: 1,
            date: new Date().toISOString().split('T')[0],
        },
    })

    const onFormSubmit = (data: DocumentFormData) => {
        const documentData: DocumentCreate = {
            ...data,
            fileId: '',
        }
        onSubmit(documentData)
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        )
    }

    return (
        <form onSubmit={handleSubmit(onFormSubmit)}>
            <FormContainer>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem
                        label={t('nav.Document.documentTitle')}
                        invalid={!!errors.title}
                        errorMessage={errors.title?.message}
                    >
                        <Input
                            {...register('title')}
                            placeholder={t(
                                'nav.Document.documentTitlePlaceholder',
                            )}
                            disabled={isSubmitting}
                        />
                    </FormItem>

                    <FormItem
                        label={t('nav.Document.documentType')}
                        invalid={!!errors.documentTypeId}
                        errorMessage={errors.documentTypeId?.message}
                    >
                        <Controller
                            name="documentTypeId"
                            control={control}
                            render={({ field }) => (
                                <Select
                                    placeholder={t(
                                        'nav.Document.selectDocumentType',
                                    )}
                                    isDisabled={isSubmitting}
                                    options={documentTypes.map(
                                        (type, index) => ({
                                            value: (index + 1).toString(),
                                            label: type.label,
                                        }),
                                    )}
                                    value={
                                        documentTypes.find(
                                            (_, index) =>
                                                index + 1 === field.value,
                                        )
                                            ? {
                                                  value: field.value.toString(),
                                                  label:
                                                      documentTypes[
                                                          field.value - 1
                                                      ]?.label || '',
                                              }
                                            : null
                                    }
                                    onChange={(selectedOption) => {
                                        field.onChange(
                                            selectedOption
                                                ? parseInt(selectedOption.value)
                                                : 1,
                                        )
                                    }}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem
                        label={t('nav.Document.generalCategory')}
                        invalid={!!errors.generalCategoryId}
                        errorMessage={errors.generalCategoryId?.message}
                    >
                        <Controller
                            name="generalCategoryId"
                            control={control}
                            render={({ field }) => (
                                <Select
                                    value={generalCategories.find(
                                        (category) =>
                                            parseInt(category.value) ===
                                            field.value,
                                    )}
                                    placeholder={t(
                                        'nav.Document.selectGeneralCategory',
                                    )}
                                    isDisabled={isSubmitting}
                                    options={generalCategories}
                                    onChange={(selectedOption) => {
                                        field.onChange(
                                            selectedOption
                                                ? parseInt(selectedOption.value)
                                                : 1,
                                        )
                                    }}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem
                        label={t('nav.Document.date')}
                        invalid={!!errors.date}
                        errorMessage={errors.date?.message}
                    >
                        <Input
                            {...register('date')}
                            type="date"
                            disabled={isSubmitting}
                        />
                    </FormItem>
                </div>

                <div className="flex items-center justify-end gap-3 mt-6">
                    <Button
                        type="button"
                        variant="plain"
                        disabled={isSubmitting}
                        onClick={onCancel}
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        loading={isSubmitting}
                        disabled={isSubmitting}
                    >
                        {t('nav.Document.addDocument')}
                    </Button>
                </div>
            </FormContainer>
        </form>
    )
}

export default DocumentsForm

import { useQuery } from '@tanstack/react-query'
import { getClosureStrapById } from '@/services/ConsumableService'

export const useGetClosureStrapById = (closureStrapId: number) => {
    return useQuery({
        queryKey: ['closure-strap', closureStrapId],
        queryFn: () => getClosureStrapById(closureStrapId),
        enabled: !!closureStrapId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}
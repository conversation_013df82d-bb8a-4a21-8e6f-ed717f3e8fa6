import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import BoxesListActionTools from './components/BoxesListActionTools'
import BoxesListTableTools from './components/BoxesListTableTools'
import BoxesListTable from './components/BoxesListTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbBox } from 'react-icons/tb'

const Boxes = () => {
    const { t } = useTranslation()
    const storage = window.localStorage
    const organizationalNodeId = storage.getItem('org_code')!

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbBox className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className="">{t('nav.boxes.boxes')}</h3>
                            </div>
                            <BoxesListActionTools
                                organizationalNodeId={organizationalNodeId}
                            />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <BoxesListTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <BoxesListTable
                                organizationalNodeId={organizationalNodeId}
                            />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>
        </>
    )
}

export default Boxes

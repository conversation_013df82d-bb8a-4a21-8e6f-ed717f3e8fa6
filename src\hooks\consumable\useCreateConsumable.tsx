import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createConsumable } from '@/services/ConsumableService'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import type { ConsumableCreate } from '@/@types/consumable'

export const useCreateConsumable = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: (consumable: ConsumableCreate) =>
            createConsumable(consumable),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            toast.push(
                <Notification title="" type="success" duration={2500}>
                    {'Consumable created successfully'}
                </Notification>,
                { placement: 'top-center' },
            )
        },
        onError: (error) => {
            console.error(error)
            // toast.push(
            //     <Notification title="" type="danger" duration={2500}>
            //         {data?.description || t('nav.systemSecurity.failed')}
            //     </Notification>,
            //     { placement: 'top-center' },
            // )
        },
    })
}

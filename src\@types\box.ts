import { PaginationResponse } from './global'

export interface Box {
    boxId: string
    organizationalNodeName: string
    numberOfFiles: number
    closureStrapSerialNumber: number
    boxStatus: number
    createdAt: string
    createdByUsername: string
}

export interface BoxDetails {
    boxId: string
    organizationalNodeId: string
    organizationalNodeName: string
    numberOfFiles: number
    closureStrapId: number
    closureStrapSerialNumber: number
    boxStatus: number
    createdByID: string
    createdByUsername: string
    createdAt: string
    updatedByID: string
    updatedByUsername: string
    updatedAt: string
}

export type BoxesResponse = {
    items: Box[]
} & PaginationResponse

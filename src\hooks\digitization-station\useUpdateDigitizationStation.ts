/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDigitizationStation } from '@/services/DigitizationStationService'
import type { DigitizationStationRequest } from '@/@types/digitizationStation'

export const useUpdateDigitizationStation = () => {
  const queryClient = useQueryClient()

  return useMutation<any, Error, { id: string | number; station: DigitizationStationRequest }>({
    mutationFn: ({ id, station }) => updateDigitizationStation(id, station),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['digitization-stations'] })
      queryClient.invalidateQueries({ queryKey: ['digitization-station', id] })
    },
    onError: (error) => {
      console.log('Error updating digitization station:', error)
    },
  })
}
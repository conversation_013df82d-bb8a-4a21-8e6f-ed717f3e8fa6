import React, { useState } from 'react'
import { Button } from '@/components/ui'
import { TbTrash, TbX } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import ConfirmDialog from '@/components/shared/ConfirmDialog'

interface GeneralCategorySelectionFooterProps {
    selectedCount: number
    onDeleteSelected: () => void
    onClearSelection: () => void
}

const GeneralCategorySelectionFooter = ({
    selectedCount,
    onDeleteSelected,
    onClearSelection,
}: GeneralCategorySelectionFooterProps) => {
    const { t } = useTranslation()
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)

    if (selectedCount === 0) {
        return null
    }

    const handleDeleteConfirm = () => {
        onDeleteSelected()
        setDeleteConfirmOpen(false)
    }

    return (
        <>
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
                <div className="container mx-auto px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <span className="text-sm font-medium text-gray-700">
                                {selectedCount}{' '}
                                {t('nav.generalCategories.generalCategoriesSelected')}
                            </span>
                        </div>

                        <div className="flex items-center gap-2">
                            <Button
                                variant="solid"
                                color="red-600"
                                size="sm"
                                icon={<TbTrash />}
                                onClick={() => setDeleteConfirmOpen(true)}
                            >
                                {t('nav.shared.delete')}
                            </Button>
                            <Button
                                variant="plain"
                                size="sm"
                                icon={<TbX />}
                                onClick={onClearSelection}
                            >
                                {t('nav.shared.clear')}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <ConfirmDialog
                isOpen={deleteConfirmOpen}
                type="danger"
                title={t('nav.generalCategories.deleteSelectedGeneralCategories')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={() => setDeleteConfirmOpen(false)}
                onRequestClose={() => setDeleteConfirmOpen(false)}
                onCancel={() => setDeleteConfirmOpen(false)}
                onConfirm={handleDeleteConfirm}
            >
                <p>
                    {t('nav.generalCategories.deleteSelectedGeneralCategoriesConfirm', {
                        count: selectedCount,
                    })}
                </p>
            </ConfirmDialog>
        </>
    )
}

export default GeneralCategorySelectionFooter

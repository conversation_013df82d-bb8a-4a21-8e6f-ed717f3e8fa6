import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { Box } from '@/@types/box'
import { useGetBoxes } from '@/hooks/boxes'

interface TransferBoxesTableProps {
    selectedBoxIds: (number | string)[]
    onSelectionChange: (ids: (number | string)[]) => void
}

const TransferBoxesTable = ({
    selectedBoxIds,
    onSelectionChange,
}: TransferBoxesTableProps) => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()

    console.log('selections', selectedBoxIds)

    const storage = window.localStorage
    const orgId = storage.getItem('org_code')!

    // Fetch boxes with status = 2 (ready for transfer)
    const { Boxes, isLoading, pagination } = useGetBoxes({
        organizationalNodeId: orgId,
        pageNumber: parseInt(searchParams.get('pageNumber') || '1', 10),
        pageSize: parseInt(searchParams.get('pageSize') || '10', 10),
        boxStatus: 1,
    })

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (size: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', size.toString())
        newSearchParams.set('pageNumber', '1') // Reset to first page
        setSearchParams(newSearchParams)
    }

    const tableColumns: ColumnDef<Box>[] = useMemo(() => {
        const cols: ColumnDef<Box>[] = [
            {
                header: t('nav.boxes.boxId'),
                accessorKey: 'boxId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.boxId}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.organization'),
                accessorKey: 'organizationalNodeName',
                cell: ({ row }) => (
                    <div className="font-medium">
                        {row.original.organizationalNodeName}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.filesCount'),
                accessorKey: 'numberOfFiles',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.numberOfFiles || 0}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.closureStrap'),
                accessorKey: 'closureStrapSerialNumber',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.closureStrapSerialNumber || 'N/A'}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.createdBy'),
                accessorKey: 'createdByUsername',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.createdByUsername}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.createdAt'),
                accessorKey: 'createdAt',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {new Date(row.original.createdAt).toLocaleDateString()}
                    </div>
                ),
            },
        ]
        return cols
    }, [t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Boxes || []}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                checkboxChecked={(row) =>
                    selectedBoxIds.includes(row.boxId as never)
                }
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
                onCheckBoxChange={(checked: boolean, row) => {
                    onSelectionChange(
                        checked
                            ? [...selectedBoxIds, row.boxId]
                            : selectedBoxIds.filter((id) => id !== row.boxId),
                    )
                }}
                onIndeterminateCheckBoxChange={(checked: boolean, rows) => {
                    if (checked) {
                        const allRowIds = rows.map((row) => row.original.boxId)
                        onSelectionChange([...selectedBoxIds, ...allRowIds])
                    } else {
                        const rowIds = rows.map((row) => row.original.boxId)
                        onSelectionChange(
                            selectedBoxIds.filter(
                                (id) => !rowIds.includes(id as never),
                            ),
                        )
                    }
                }}
            />
        </>
    )
}

export default TransferBoxesTable

import { useQuery } from '@tanstack/react-query'
import { getDocumentsByFile } from '@/services/DocumentsService'
import { useSearchParams } from 'react-router-dom'
import { Status } from '@/@types/common'
import { PaginationResponse } from '@/@types/global'

export const useGetDocumentsByFile = (fileId: string) => {
    const [searchParams] = useSearchParams()

    const pageNumber = parseInt(searchParams.get('pageNumber') || '1', 10)
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10)
    const statusParam = searchParams.get('status')
    const status =
        statusParam !== null ? (parseInt(statusParam, 10) as Status) : undefined

    const query = useQuery({
        queryKey: ['documents', 'file', fileId, pageNumber, pageSize, status],
        queryFn: () =>
            getDocumentsByFile(fileId, {
                pageNumber,
                pageSize,
                status,
            }),
        enabled: !!fileId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })

    return {
        ...query,
        Documents: query.data?.items,
        pagination: query.data as PaginationResponse,
    }
}

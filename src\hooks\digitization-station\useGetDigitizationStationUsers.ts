import { useQuery } from '@tanstack/react-query'
import { getDigitizationStationUsers } from '@/services/DigitizationStationService'

export const useGetDigitizationStationUsers = (id: string | number) => {
  return useQuery<Array<{ id: string; fullName: string }>>({
    queryKey: ['digitization-station', id, 'users'],
    queryFn: () => getDigitizationStationUsers(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}
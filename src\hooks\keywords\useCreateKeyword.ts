import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createKeywordApi } from '@/services/Keywords'

export const useCreateKeyword = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: createKeyword<PERSON><PERSON>,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['keywords'] })
        },
        onError: (error) => {
            console.error('Error creating keyword:', error)
        },
    })
}

export default useCreateKeyword

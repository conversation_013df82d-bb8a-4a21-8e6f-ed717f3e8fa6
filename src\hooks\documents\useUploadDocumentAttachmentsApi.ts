import { useMutation, useQueryClient } from '@tanstack/react-query'
import { uploadDocumentAttachmentsApi } from '@/services/DocumentsService'

export const useUploadDocumentAttachments = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ documentId, attachments }: { documentId: string; attachments: File[] }) =>
            uploadDocumentAttachmentsApi(documentId, attachments),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['documents'] })
        },
        onError: (error) => {
            console.log(error?.message || 'Failed to upload document attachments')
        },
    })
}
import { useQuery } from '@tanstack/react-query'
import { getAccountApi } from '@/services/AuthService'

export function useGetAccount() {
    const token = localStorage.getItem('token')
    
    return useQuery({
        queryKey: ['account'],
        queryFn: getAccountApi,
        enabled: !!token,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })
}

import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Button from '@/components/ui/Button'
import Select from '@/components/ui/Select'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbCheck, TbX } from 'react-icons/tb'
import { z } from 'zod'

// Form validation schema
const boxFormSchema = z.object({
    organizationalNodeId: z.number().min(1, 'Organization is required'),
})

type BoxFormData = z.infer<typeof boxFormSchema>

interface BoxesFormProps {
    mode: 'create' | 'edit'
    boxId?: string
    onSubmit: (data: BoxFormData) => Promise<void>
    onCancel: () => void
    isSubmitting: boolean
}

const BoxesForm = ({
    mode,
    boxId,
    onSubmit,
    onCancel,
    isSubmitting,
}: BoxesFormProps) => {
    const { t } = useTranslation()

    const defaultValues: BoxFormData = {
        organizationalNodeId: 1, // Default organization
    }

    const {
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<BoxFormData>({
        defaultValues,
        resolver: zodResolver(boxFormSchema),
    })

    // Mock organization options - replace with actual data
    const organizationOptions = [
        { value: 1, label: 'Organization 1' },
        { value: 2, label: 'Organization 2' },
        { value: 3, label: 'Organization 3' },
    ]

    const onFormSubmit = async (data: BoxFormData) => {
        try {
            await onSubmit(data)
        } catch (error) {
            console.error('Form submission error:', error)
        }
    }

    return (
        <Form>
            <div className="space-y-4">
                <FormItem
                    label={t('nav.boxes.organization')}
                    invalid={!!errors.organizationalNodeId}
                    errorMessage={errors.organizationalNodeId?.message}
                >
                    <Controller
                        name="organizationalNodeId"
                        control={control}
                        render={({ field }) => (
                            <Select
                                value={organizationOptions.find(
                                    (option) => option.value === field.value,
                                )}
                                options={organizationOptions}
                                placeholder={t('nav.boxes.selectOrganization')}
                                isDisabled={isSubmitting || mode === 'edit'}
                                menuPlacement="auto"
                                onChange={(option) =>
                                    field.onChange(option?.value)
                                }
                            />
                        )}
                    />
                </FormItem>

                {mode === 'edit' && boxId && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-600">
                            <strong>{t('nav.boxes.boxId')}:</strong> {boxId}
                        </p>
                    </div>
                )}
            </div>

            <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200 mt-6">
                <Button
                    type="button"
                    variant="plain"
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                    onClick={onCancel}
                >
                    <TbX className="text-lg" />
                    {t('nav.shared.cancel')}
                </Button>
                <Button
                    type="submit"
                    variant="solid"
                    loading={isSubmitting}
                    className="flex items-center gap-2"
                    onClick={handleSubmit(onFormSubmit)}
                >
                    <TbCheck className="text-lg" />
                    {mode === 'create' ? t('nav.shared.create') : t('nav.shared.update')}
                </Button>
            </div>
        </Form>
    )
}

export default BoxesForm

import { useTranslation } from 'react-i18next'
import { DocumentDetails } from '@/@types/document'
import { Card } from '@/components/ui'
import AttachmentsUpload from './AttachmentsUpload'
import UpdateDocumentData from './UpdateDocumentData'
import { TbFile } from 'react-icons/tb'

interface DocumentMetadataProps {
    document: DocumentDetails | null
    loading?: boolean
}

export default function DocumentMetadata({
    document,
    loading = false,
}: DocumentMetadataProps) {
    const { t } = useTranslation()

    if (loading) {
        return (
            <Card className="p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
                    <div className="space-y-3">
                        {[...Array(6)].map((_, i) => (
                            <div
                                key={i}
                                className="flex items-center space-x-3"
                            >
                                <div className="w-5 h-5 bg-gray-200 rounded"></div>
                                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        ))}
                    </div>
                </div>
            </Card>
        )
    }

    if (!document) {
        return (
            <Card className="p-6">
                <div className="text-center text-gray-500">
                    <TbFile className="mx-auto text-4xl mb-2" />
                    <p>{t('documents.noDocumentSelected')}</p>
                </div>
            </Card>
        )
    }

    const handleRefresh = () => {
        // Refresh logic can be added here
        console.log('Refreshing document data...')
    }

    return (
        <div className="space-y-6">
            {/* Update Document Data Section */}
            <UpdateDocumentData
                document={document}
                onUpdateSuccess={handleRefresh}
            />
            {/* Attachments Upload Section */}
            {document && (
                <AttachmentsUpload
                    documentId={document.documentId}
                    onUploadSuccess={handleRefresh}
                />
            )}
        </div>
    )
}

import React, { useState } from 'react'
import { Button, Dropdown, Checkbox } from '@/components/ui'
import { TbAdjustments } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'

interface GeneralCategoryFilterProps {
    onColumnVisibilityChange?: (columns: string[]) => void
}

const GeneralCategoryFilter = ({ onColumnVisibilityChange }: GeneralCategoryFilterProps) => {
    const { t } = useTranslation()
    const [visibleColumns, setVisibleColumns] = useState([
        'id',
        'name',
        'actions',
    ])

    const availableColumns = [
        { key: 'id', label: t('nav.shared.id') },
        { key: 'name', label: t('nav.generalCategories.name') },
        { key: 'actions', label: t('nav.shared.actions') },
    ]

    const handleColumnToggle = (columnKey: string, checked: boolean) => {
        let newColumns
        if (checked) {
            newColumns = [...visibleColumns, columnKey]
        } else {
            newColumns = visibleColumns.filter((col) => col !== columnKey)
        }
        setVisibleColumns(newColumns)
        onColumnVisibilityChange?.(newColumns)
    }

    return (
        <Dropdown
            renderTitle={
                <Button variant="default" size="sm" icon={<TbAdjustments />}>
                    {t('nav.shared.columns')}
                </Button>
            }
        >
            <div className="p-2 min-w-[200px]">
                <div className="mb-2">
                    <span className="text-sm font-medium text-gray-700">
                        {t('nav.shared.showColumns')}
                    </span>
                </div>
                {availableColumns.map((column) => (
                    <div key={column.key} className="flex items-center py-1">
                        <Checkbox
                            checked={visibleColumns.includes(column.key)}
                            onChange={(checked) =>
                                handleColumnToggle(column.key, checked)
                            }
                        />
                        <span className="ml-2 text-sm">{column.label}</span>
                    </div>
                ))}
            </div>
        </Dropdown>
    )
}

export default GeneralCategoryFilter

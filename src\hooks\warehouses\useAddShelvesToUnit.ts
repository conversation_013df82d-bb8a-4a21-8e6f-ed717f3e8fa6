/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { addShelvesToUnit } from '@/services/Warehouses'
import type { WarehouseShelf } from '@/@types/warehouse'

export const useAddShelvesToUnit = () => {
    const queryClient = useQueryClient()

    return useMutation<
        any,
        Error,
        {
            warehouseId: string
            areaId: string
            unitId: string
            shelves: WarehouseShelf
        }
    >({
        mutationFn: ({ warehouseId, areaId, unitId, shelves }) =>
            addShelvesToUnit(warehouseId, areaId, unitId, shelves),
        onSuccess: (_, { warehouseId, areaId, unitId }) => {
            queryClient.invalidateQueries({
                queryKey: ['warehouse', warehouseId],
            })
            queryClient.invalidateQueries({
                queryKey: [
                    'warehouse',
                    warehouseId,
                    'area',
                    areaId,
                    'unit',
                    unitId,
                ],
            })
        },
        onError: (error) => {
            console.log('Error adding shelves to unit:', error)
        },
    })
}

import { useQuery } from '@tanstack/react-query'
import { getDistricts } from '@/services/LocationService'
import type { District } from '@/@types/location'

export const useGetDistricts = (cityCode: string) => {
  return useQuery<District[]>({
    queryKey: ['districts', cityCode],
    queryFn: () => getDistricts(cityCode),
    enabled: !!cityCode,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 3,
  })
}
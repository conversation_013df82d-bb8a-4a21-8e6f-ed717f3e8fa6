import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateDocument } from '@/services/DocumentsService'
import { DocumentUpdate } from '@/@types/document'

export const useUpdateDocument = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({
            documentId,
            document,
        }: {
            documentId: string
            document: DocumentUpdate
        }) => updateDocument(documentId, document),
        onSuccess: (documentId) => {
            queryClient.invalidateQueries({
                queryKey: ['document', documentId],
            })
            queryClient.invalidateQueries({ queryKey: ['documents'] })
        },
        onError: (error) => {
            console.log(error?.message || 'Failed to update document')
        },
    })
}

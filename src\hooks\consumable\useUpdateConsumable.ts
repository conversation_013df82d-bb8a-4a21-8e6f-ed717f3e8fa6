import { useMutation, useQueryClient } from '@tanstack/react-query'
import { updateConsumable } from '@/services/ConsumableService'
import type { ConsumableUpdata } from '@/@types/consumable'

export const useUpdateConsumable = () => {
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: ({ id, consumable }: { id: number; consumable: ConsumableUpdata }) => 
            updateConsumable(id, consumable),
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['consumables'] })
            queryClient.invalidateQueries({ queryKey: ['consumable', variables.id] })
        },
        onError: (error) => {
            console.error(error)
        },
    })
}
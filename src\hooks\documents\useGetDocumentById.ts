import { useQuery } from '@tanstack/react-query'
import { getDocumentById } from '@/services/DocumentsService'
import { DocumentDetails } from '@/@types/document'

export const useGetDocumentById = (documentId: string) => {
  return useQuery<DocumentDetails>({
    queryKey: ['document', documentId],
    queryFn: () => getDocumentById(documentId),
    enabled: !!documentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createGeneralCategory } from '@/services/GeneralCategoriesService'

export const useCreateGeneralCategory = () => {
    const queryClient = useQueryClient()
    return useMutation({
        mutationFn: createGeneralCategory,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['generalCategories'] })
        },
        onError: (error) => {
            console.log('Error creating general category:', error)
        },
    })
}

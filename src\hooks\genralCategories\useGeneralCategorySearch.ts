import { useState, useCallback, useMemo } from 'react'
import { useGetGeneralCategories } from './useGetGeneralCategories'
import type { GeneralCategory } from '@/services/GeneralCategoriesService'

type UseGeneralCategorySearchReturn = {
    isLoading: boolean
    error: string | null
    results: GeneralCategory[]
    search: (term: string) => void
    searchTerm: string
}

export function useGeneralCategorySearch(): UseGeneralCategorySearchReturn {
    const { data: generalCategories = [], isLoading, error } = useGetGeneralCategories()
    const [searchTerm, setSearchTerm] = useState('')

    const search = useCallback((term: string) => {
        setSearchTerm(term)
    }, [])

    const results = useMemo(() => {
        if (!searchTerm.trim()) {
            return generalCategories
        }

        const lowercaseSearchTerm = searchTerm.toLowerCase()
        return generalCategories.filter((category) =>
            category.name.toLowerCase().includes(lowercaseSearchTerm) ||
            category.id.toString().includes(lowercaseSearchTerm)
        )
    }, [generalCategories, searchTerm])

    return {
        isLoading,
        error: error?.message || null,
        results,
        search,
        searchTerm,
    }
}

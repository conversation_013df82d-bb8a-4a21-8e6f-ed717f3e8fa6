import { useQuery } from '@tanstack/react-query'
import { getFilesByStation } from '@/services/FilesService'
import { useSearchParams } from 'react-router-dom'
import { PaginationResponse } from '@/@types/global'

export const useGetFilesByStation = ({
    stationId,
    page,
    size,
    status,
}: {
    stationId: number
    page?: number
    size?: number
    status?: number
}) => {
    const [searchParams] = useSearchParams()

    // Use provided options or fall back to URL params or defaults
    const pageNumber =
        page ?? parseInt(searchParams.get('pageNumber') || '1', 10)
    const pageSize = size ?? parseInt(searchParams.get('pageSize') || '10', 10)

    // If status is provided in options, use it; otherwise check URL params; default to 1
    let fileStatus: number | undefined
    if (status !== undefined) {
        fileStatus = status
    } else {
        const statusParam = searchParams.get('fileStatus')
        fileStatus =
            statusParam !== null ? parseInt(statusParam, 10) : undefined
    }

    const query = useQuery({
        queryKey: ['files', stationId, pageNumber, pageSize, fileStatus],
        queryFn: () =>
            getFilesByStation(stationId, {
                pageNumber,
                pageSize,
                fileStatus,
            }),
        enabled: !!stationId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: 3,
    })

    return {
        ...query,
        Files: query.data?.items,
        pagination: query.data as PaginationResponse,
    }
}

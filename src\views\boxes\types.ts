export enum BoxStatus {
    Open = 0,
    Closed = 1,
    Transferred = 2,
}

export const getBoxStatusConfig = (t: (key: string) => string) => ({
    [BoxStatus.Open]: t('boxStatus.open'),
    [BoxStatus.Closed]: t('boxStatus.closed'),
    [BoxStatus.Transferred]: t('boxStatus.transferred'),
})

export const getBoxStatusStyle = (status: BoxStatus) => {
    switch (status) {
        case BoxStatus.Open:
            return {
                color: '#4CAF50', // Green
                backgroundColor: '#E8F5E9',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        case BoxStatus.Closed:
            return {
                color: '#F44336', // Red
                backgroundColor: '#FFEBEE',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        case BoxStatus.Transferred:
            return {
                color: '#2196F3', // Blue
                backgroundColor: '#E3F2FD',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        default:
            return {}
    }
}

import { useQuery } from '@tanstack/react-query'
import { getCategoryTree } from '@/services/CategoriesService'
import type { CategoryTree } from '@/@types/categories'
import { Status } from '@/@types/common'

interface CategoryTreeParams {
    statusFilter?: Status
    includeDeleted?: boolean
}

export const useGetCategoryTree = (params?: CategoryTreeParams) => {
    return useQuery<CategoryTree[]>({
        queryKey: [
            'categories',
            'tree',
            params?.statusFilter,
            params?.includeDeleted,
        ],
        queryFn: () =>
            getCategoryTree({
                status: params?.statusFilter,
                includeDeleted: params?.includeDeleted ?? false,
            }),
        staleTime: 10 * 60 * 1000, // 10 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
        retry: 3,
    })
}
